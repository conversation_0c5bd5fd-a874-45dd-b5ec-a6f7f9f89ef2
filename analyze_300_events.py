import pandas as pd
from ltp import LTP
import os
import datetime

# 定义强弱关系关键词
POWER_RELATION_KEYWORDS = {
    "殴打", "欺凌", "欺负", "霸凌", "暴力", "打击", "压迫", "威胁", "恐吓", 
    "强制", "逼迫", "迫害", "虐待", "侮辱", "羞辱", "辱骂", "歧视",
    "排挤", "孤立", "针对", "报复", "惩罚", "制裁", "镇压", "打压",
    "围攻", "围堵", "围殴", "群殴", "追打", "殴伤", "打伤", "踢打",
    "推搡", "拖拽", "强拉", "强行", "野蛮", "粗暴", "凶狠", "残忍"
}

# 定义强势群体和弱势群体
STRONG_GROUPS = {"城管", "警察", "老师", "教官", "政府", "保安", "医生", "法官", "班主任", "领导", "官员"}
WEAK_GROUPS = {"学生", "小贩", "摊贩", "市民", "孩子", "女孩", "老人", "男童", "女童", "百姓", "居民", "患者"}

def analyze_300_events():
    """
    分析300事件文件
    """
    print("=== 300事件强弱关系分析系统 ===")
    
    # 读取数据
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")

    # 尝试不同的文件格式
    file_formats = ["300事件.xlsx", "300事件.xls", "300事件.csv"]
    df = None

    for file_format in file_formats:
        file_path = os.path.join(desktop_path, file_format)
        try:
            if file_format.endswith('.csv'):
                df = pd.read_csv(file_path, encoding='utf-8')
            else:
                df = pd.read_excel(file_path)
            print(f"成功读取文件: {file_format}，共 {len(df)} 条数据")
            print(f"列名: {list(df.columns)}")
            break
        except FileNotFoundError:
            continue
        except Exception as e:
            print(f"读取文件 {file_format} 时出错: {e}")
            continue

    if df is None:
        print("未找到300事件文件，请确保桌面上有以下文件之一:")
        for fmt in file_formats:
            print(f"  - {fmt}")
        return
    
    print("正在初始化LTP模型...")
    ltp = LTP()
    print("LTP初始化完成")
    
    # 获取第一列（新闻标题）
    title_column = df.columns[0]
    titles = df[title_column].dropna().astype(str)
    
    results = []
    
    for idx, title in enumerate(titles):
        print(f"\n正在分析第 {idx+1}/{len(titles)} 条:")
        print(f"标题: {title}")
        
        try:
            # 1. 判断是否存在强弱关系
            has_power_relation = detect_power_relation(title)
            
            # 2. LTP分析
            result = ltp.pipeline([title], tasks=["cws", "pos", "dep"])
            words = result.cws[0]
            pos_tags = result.pos[0]
            dependencies = list(zip(result.dep[0]['head'], result.dep[0]['label']))
            
            print(f"分词: {' / '.join(words)}")
            
            # 3. 提取人物主谓宾
            subject, predicate, object_word = extract_person_svo(words, pos_tags, dependencies)
            
            print(f"强弱关系: {'是' if has_power_relation else '否'}")
            print(f"主语(人物): {subject}")
            print(f"谓语: {predicate}")
            print(f"宾语(人物): {object_word}")
            
            result_data = {
                '新闻标题': title,
                '是否存在强弱关系': '是' if has_power_relation else '否',
                '主语': subject,
                '谓语': predicate,
                '宾语': object_word
            }
            results.append(result_data)
            
        except Exception as e:
            print(f"分析出错: {e}")
            result_data = {
                '新闻标题': title,
                '是否存在强弱关系': '分析失败',
                '主语': '分析失败',
                '谓语': '分析失败',
                '宾语': '分析失败'
            }
            results.append(result_data)
    
    # 保存结果
    result_df = pd.DataFrame(results)
    output_path = os.path.join(desktop_path, "300事件结果.xlsx")
    result_df.to_excel(output_path, index=False)
    
    print(f"\n=== 分析完成 ===")
    print(f"结果已保存到: {output_path}")
    
    # 统计分析
    print_analysis_statistics(results)

def detect_power_relation(title):
    """
    检测标题中是否存在强弱关系
    """
    # 检查是否包含强弱关系关键词
    for keyword in POWER_RELATION_KEYWORDS:
        if keyword in title:
            return True
    
    # 检查是否同时包含强势群体和弱势群体
    has_strong = any(group in title for group in STRONG_GROUPS)
    has_weak = any(group in title for group in WEAK_GROUPS)
    
    if has_strong and has_weak:
        # 进一步检查是否有冲突性词汇
        conflict_words = {"冲突", "争执", "纠纷", "对抗", "抗议", "反抗", "斗争"}
        if any(word in title for word in conflict_words):
            return True
    
    return False

def extract_person_svo(words, pos_tags, dependencies):
    """
    提取人物主谓宾
    """
    # 扩展的人物关键词库
    person_keywords = {
        '城管', '警察', '老师', '教官', '政府', '保安', '医生', '法官', '班主任',
        '学生', '小贩', '摊贩', '市民', '孩子', '女孩', '老人', '男童', '女童', 
        '百姓', '居民', '商贩', '店主', '男子', '女子', '大爷', '大妈', '师傅',
        '人', '商户', '摊主', '贩子', '小商贩', '患者', '家长', '员工', '工人',
        '农民', '司机', '乘客', '顾客', '村民', '居民', '市民', '民众', '群众',
        '同学', '同事', '朋友', '邻居', '路人', '行人', '旅客', '游客', '网友'
    }
    
    subject = "无"
    predicate = "未识别"
    object_word = "无"
    
    # 寻找主要动词
    verb_indices = []
    for i, pos in enumerate(pos_tags):
        if pos.startswith('v'):
            verb_indices.append(i)
    
    if verb_indices:
        # 选择最重要的动词
        predicate_idx = find_main_verb(verb_indices, words, dependencies)
        predicate = words[predicate_idx]
        
        # 寻找人物主语（在动词前）
        subject = find_person_subject(words, pos_tags, predicate_idx, person_keywords)
        
        # 寻找人物宾语（在动词后）
        object_word = find_person_object(words, pos_tags, predicate_idx, person_keywords)
    
    return subject, predicate, object_word

def find_main_verb(verb_indices, words, dependencies):
    """
    找到主要动词
    """
    # 优先选择根节点
    for i, (head, relation) in enumerate(dependencies):
        if head == 0 and i in verb_indices:
            return i
    
    # 如果没有根节点动词，选择第一个动词
    return verb_indices[0]

def find_person_subject(words, pos_tags, predicate_idx, person_keywords):
    """
    寻找人物主语
    """
    # 在动词前寻找人物
    for i in range(predicate_idx-1, -1, -1):
        if is_person_entity(words[i], person_keywords, pos_tags[i]):
            return build_person_entity(words, i, "backward")
    
    return "无"

def find_person_object(words, pos_tags, predicate_idx, person_keywords):
    """
    寻找人物宾语
    """
    # 在动词后寻找人物
    for i in range(predicate_idx+1, len(words)):
        if is_person_entity(words[i], person_keywords, pos_tags[i]):
            return build_person_entity(words, i, "forward")
    
    return "无"

def is_person_entity(word, person_keywords, pos_tag):
    """
    判断词语是否是人物实体
    """
    # 直接匹配人物关键词
    if word in person_keywords:
        return True
    
    # 包含人物关键词
    if any(keyword in word for keyword in person_keywords):
        return True
    
    # 人物词汇模式
    if pos_tag.startswith('n') or pos_tag.startswith('r'):
        person_suffixes = ['人', '者', '员', '师', '长', '手', '工', '民', '客', '户', '贩', '主']
        if any(word.endswith(suffix) for suffix in person_suffixes) and len(word) > 1:
            return True
        
        # 特殊前缀
        if word.startswith('老') and len(word) > 1:
            return True
        if word.startswith('小') and len(word) > 1 and word not in ['小区', '小车', '小店', '小时', '小组']:
            return True
        if word.startswith('大') and word in ['大爷', '大妈', '大叔', '大婶', '大哥', '大姐']:
            return True
    
    return False

def build_person_entity(words, person_idx, direction):
    """
    构建完整的人物实体
    """
    entity_parts = [words[person_idx]]
    
    if direction == "backward":
        # 向前收集修饰词
        for i in range(person_idx-1, -1, -1):
            word = words[i]
            if (word in ['一', '某', '该', '这', '那', '两', '三', '多', '几', '名', '位', '个'] or 
                is_location_word(word) or 
                is_age_word(word) or
                (len(word) <= 3 and not word in ['被', '把', '将', '给', '对', '向', '从', '在'])):
                entity_parts.insert(0, word)
            else:
                break
    elif direction == "forward":
        # 向后收集修饰词（较少使用）
        for i in range(person_idx+1, len(words)):
            word = words[i]
            if len(word) <= 2 and word not in ['的', '了', '着', '过', '被', '把', '将', '和', '与']:
                entity_parts.append(word)
            else:
                break
    
    return "".join(entity_parts)

def is_location_word(word):
    """
    判断是否是地点词
    """
    locations = [
        '北京', '上海', '广州', '深圳', '杭州', '南京', '武汉', '成都', '重庆', '天津',
        '西安', '沈阳', '大连', '青岛', '厦门', '苏州', '宁波', '合肥', '邢台', '孝感',
        '衡阳', '太原', '东莞', '南江', '云南', '辽宁', '山东', '江苏', '浙江', '广东',
        '湖北', '湖南', '河南', '河北', '山西', '陕西', '四川', '贵州', '福建', '安徽'
    ]
    return any(loc in word for loc in locations)

def is_age_word(word):
    """
    判断是否是年龄词
    """
    age_words = ['岁', '年级', '级', '年', '月']
    return any(age in word for age in age_words) or word.isdigit()

def print_analysis_statistics(results):
    """
    打印统计分析
    """
    print("\n=== 统计分析 ===")
    
    total = len(results)
    
    # 强弱关系统计
    power_relation_count = sum(1 for r in results if r['是否存在强弱关系'] == '是')
    print(f"总标题数: {total}")
    print(f"存在强弱关系: {power_relation_count} ({power_relation_count/total*100:.1f}%)")
    print(f"不存在强弱关系: {total-power_relation_count} ({(total-power_relation_count)/total*100:.1f}%)")
    
    # 主谓宾统计
    has_subject = sum(1 for r in results if r['主语'] != '无' and r['主语'] != '分析失败')
    has_predicate = sum(1 for r in results if r['谓语'] != '未识别' and r['谓语'] != '分析失败')
    has_object = sum(1 for r in results if r['宾语'] != '无' and r['宾语'] != '分析失败')
    has_complete_svo = sum(1 for r in results if 
                          r['主语'] != '无' and r['主语'] != '分析失败' and
                          r['谓语'] != '未识别' and r['谓语'] != '分析失败' and
                          r['宾语'] != '无' and r['宾语'] != '分析失败')
    
    print(f"\n主谓宾提取统计:")
    print(f"有人物主语: {has_subject} ({has_subject/total*100:.1f}%)")
    print(f"有谓语: {has_predicate} ({has_predicate/total*100:.1f}%)")
    print(f"有人物宾语: {has_object} ({has_object/total*100:.1f}%)")
    print(f"完整主谓宾: {has_complete_svo} ({has_complete_svo/total*100:.1f}%)")
    
    # 强弱关系与主谓宾的关联分析
    power_with_complete_svo = sum(1 for r in results if 
                                 r['是否存在强弱关系'] == '是' and
                                 r['主语'] != '无' and r['主语'] != '分析失败' and
                                 r['宾语'] != '无' and r['宾语'] != '分析失败')
    
    if power_relation_count > 0:
        print(f"\n关联分析:")
        print(f"有强弱关系且有完整主谓宾: {power_with_complete_svo} ({power_with_complete_svo/power_relation_count*100:.1f}%)")

if __name__ == "__main__":
    analyze_300_events()
