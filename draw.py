import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据文件
def load_data():
    """读取数据文件，尝试不同编码"""
    # 优先尝试读取已处理的文件
    filename = '335南江城管与卖菜老人推搡事件.csv'

    try:
        df = pd.read_csv(filename, encoding='utf-8-sig')
        print(f"成功读取文件: {filename}")
        return df
    except FileNotFoundError:
        print(f"文件 {filename} 不存在，尝试读取原始文件...")

    # 如果处理后的文件不存在，读取原始文件
    filename = '335南江城管与卖菜老人推搡事件.csv'
    try:
        df = pd.read_csv(filename, encoding='gb18030')
        print(f"成功读取原始文件: {filename}")
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(filename, encoding='utf-8')
            print(f"成功读取原始文件: {filename} (UTF-8)")
        except UnicodeDecodeError:
            df = pd.read_csv(filename, encoding='gbk')
            print(f"成功读取原始文件: {filename} (GBK)")

    # 检查是否有用户划分列
    if '用户划分' not in df.columns:
        print("错误：数据文件中没有'用户划分'列，请先运行 divide.py 生成用户划分数据")
        return None

    return df

def process_date_column(df):
    """处理日期列，转换为标准日期格式"""
    print("正在处理日期列...")

    # 尝试直接解析日期
    try:
        df['日期_处理'] = pd.to_datetime(df['日期'], errors='coerce')
        print("日期解析成功")
    except:
        print("日期解析失败，尝试其他方法...")
        df['日期_处理'] = pd.to_datetime(df['日期'], errors='coerce')

    # 检查解析结果
    valid_dates = df['日期_处理'].notna().sum()
    total_dates = len(df)
    print(f"成功解析日期: {valid_dates}/{total_dates} ({valid_dates/total_dates*100:.1f}%)")

    # 只保留有效日期的数据
    df_valid = df[df['日期_处理'].notna()].copy()

    # 提取日期部分（去掉时间）
    df_valid['日期_日'] = df_valid['日期_处理'].dt.date

    return df_valid

def calculate_slope(x1, y1, x2, y2):
    """计算两点之间的斜率，x为日期"""
    # 将日期转换为数值（天数差）
    if hasattr(x1, 'toordinal'):
        x1_num = x1.toordinal()
        x2_num = x2.toordinal()
    else:
        x1_num = pd.to_datetime(x1).toordinal()
        x2_num = pd.to_datetime(x2).toordinal()

    if x2_num == x1_num:
        return 0
    return (y2 - y1) / (x2_num - x1_num)

def find_auxiliary_points(total_data):
    """找到总计线峰值左右的辅助线点"""
    print("正在寻找辅助线点...")

    # 找到峰值点
    peak_idx = total_data['数量'].idxmax()
    peak_value = total_data.loc[peak_idx, '数量']
    peak_date = total_data.loc[peak_idx, '日期_日']

    print(f"峰值点: 日期={peak_date}, 数量={peak_value}, 索引={peak_idx}")

    # 计算所有相邻点的斜率
    slopes = []
    print("\n=== 斜率计算详情 ===")
    for i in range(len(total_data) - 1):
        x1 = total_data.iloc[i]['日期_日']
        y1 = total_data.iloc[i]['数量']
        x2 = total_data.iloc[i + 1]['日期_日']
        y2 = total_data.iloc[i + 1]['数量']
        slope = calculate_slope(x1, y1, x2, y2)
        slopes.append(slope)
        print(f"点{i}到点{i+1}: ({x1}, {y1}) -> ({x2}, {y2}), 斜率 = {slope:.4f}")

    print(f"\n所有斜率: {[round(s, 4) for s in slopes]}")

    # 找到峰值左侧最近的斜率符号变化点（A左点）
    left_a_idx = None
    print(f"\n=== 寻找左侧A点 ===")
    print(f"从峰值索引{peak_idx}向左搜索斜率符号变化点...")
    for i in range(peak_idx - 1, 0, -1):  # 从峰值向左搜索
        if i - 1 < len(slopes) and i < len(slopes):
            current_slope = slopes[i] if i >= 0 else 0
            prev_slope = slopes[i-1] if i - 1 >= 0 else 0

            print(f"检查索引{i}: 后一斜率={current_slope:.4f}, 前一斜率={prev_slope:.4f}")

            # 检查斜率符号是否相反
            if (current_slope > 0 and prev_slope < 0) or (current_slope < 0 and prev_slope > 0):
                left_a_idx = i
                print(f"找到左侧A点! 索引={i}, 斜率符号变化: {prev_slope:.4f} -> {current_slope:.4f}")
                break

    # 找到峰值右侧最近的斜率符号变化点（A右点）
    right_a_idx = None
    print(f"\n=== 寻找右侧A点 ===")
    print(f"从峰值索引{peak_idx}向右搜索斜率符号变化点...")
    for i in range(peak_idx + 1, len(total_data) - 1):  # 从峰值向右搜索
        if i < len(slopes) and i + 1 < len(slopes):
            current_slope = slopes[i]
            next_slope = slopes[i + 1] if i + 1 < len(slopes) else 0

            print(f"检查索引{i}: 当前斜率={current_slope:.4f}, 下一斜率={next_slope:.4f}")

            # 检查斜率符号是否相反
            if (current_slope > 0 and next_slope < 0) or (current_slope < 0 and next_slope > 0):
                right_a_idx = i + 1
                print(f"找到右侧A点! 索引={i+1}, 斜率符号变化: {current_slope:.4f} -> {next_slope:.4f}")
                break

    print(f"A左点索引: {left_a_idx}, A右点索引: {right_a_idx}")

    # 从A点向峰值方向找到|k|>25的点
    left_aux_idx = None
    right_aux_idx = None

    # 左侧辅助点
    print(f"\n=== 寻找左侧辅助点 ===")
    if left_a_idx is not None:
        print(f"从A左点索引{left_a_idx}向峰值索引{peak_idx}搜索|k|>25的点...")
        for i in range(left_a_idx, peak_idx):
            if i + 1 < len(total_data):
                x1 = total_data.iloc[i]['日期_日']
                y1 = total_data.iloc[i]['数量']
                x2 = total_data.iloc[i + 1]['日期_日']
                y2 = total_data.iloc[i + 1]['数量']
                slope = calculate_slope(x1, y1, x2, y2)

                print(f"检查索引{i}到{i+1}: ({x1}, {y1}) -> ({x2}, {y2}), 斜率={slope:.4f}, |k|={abs(slope):.4f}")

                if abs(slope) > 25:
                    left_aux_idx = i
                    print(f"找到左侧辅助点! 索引={i}, |k|={abs(slope):.4f} > 25")
                    break
    else:
        print("未找到A左点，无法确定左侧辅助点")

    # 右侧辅助点
    print(f"\n=== 寻找右侧辅助点 ===")
    if right_a_idx is not None:
        print(f"从A右点索引{right_a_idx}向峰值索引{peak_idx}搜索|k|>25的点...")
        for i in range(right_a_idx, peak_idx, -1):  # 从A右点向峰值方向搜索
            if i - 1 >= 0:
                x1 = total_data.iloc[i - 1]['日期_日']
                y1 = total_data.iloc[i - 1]['数量']
                x2 = total_data.iloc[i]['日期_日']
                y2 = total_data.iloc[i]['数量']
                slope = calculate_slope(x1, y1, x2, y2)

                print(f"检查索引{i-1}到{i}: ({x1}, {y1}) -> ({x2}, {y2}), 斜率={slope:.4f}, |k|={abs(slope):.4f}")

                if abs(slope) > 25:
                    right_aux_idx = i
                    print(f"找到右侧辅助点! 索引={i}, |k|={abs(slope):.4f} > 25")
                    break
    else:
        print("未找到A右点，无法确定右侧辅助点")

    result = {
        'peak_idx': peak_idx,
        'peak_date': peak_date,
        'peak_value': peak_value,
        'left_aux_idx': left_aux_idx,
        'right_aux_idx': right_aux_idx,
        'left_a_idx': left_a_idx,
        'right_a_idx': right_a_idx
    }

    # 添加辅助点的详细信息
    if left_aux_idx is not None:
        result['left_aux_date'] = total_data.iloc[left_aux_idx]['日期_日']
        result['left_aux_value'] = total_data.iloc[left_aux_idx]['数量']
        print(f"左侧辅助点: 日期={result['left_aux_date']}, 数量={result['left_aux_value']}, 索引={left_aux_idx}")

    if right_aux_idx is not None:
        result['right_aux_date'] = total_data.iloc[right_aux_idx]['日期_日']
        result['right_aux_value'] = total_data.iloc[right_aux_idx]['数量']
        print(f"右侧辅助点: 日期={result['right_aux_date']}, 数量={result['right_aux_value']}, 索引={right_aux_idx}")

    return result

def create_stacked_bar_chart(df, auxiliary_points):
    """创建按时期划分的堆叠柱状图"""
    print("\n开始创建堆叠柱状图...")

    # 获取所有非空的用户划分类别
    user_categories = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')]['用户划分'].unique()
    user_categories = [cat for cat in user_categories if cat.strip() != '']

    if len(user_categories) == 0:
        print("没有有效的用户划分数据，无法绘制堆叠柱状图")
        return

    # 只使用有用户划分的数据
    valid_data = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')].copy()

    # 获取辅助线日期
    if auxiliary_points['left_aux_idx'] is not None and auxiliary_points['right_aux_idx'] is not None:
        left_aux_date = auxiliary_points['left_aux_date']
        right_aux_date = auxiliary_points['right_aux_date']

        print(f"左侧辅助线日期: {left_aux_date}")
        print(f"右侧辅助线日期: {right_aux_date}")

        # 按时期划分数据
        # 潜伏期：左辅助线之前
        latent_data = valid_data[valid_data['日期_日'] < left_aux_date]
        # 爆发期：左辅助线到右辅助线之间
        outbreak_data = valid_data[(valid_data['日期_日'] >= left_aux_date) & (valid_data['日期_日'] <= right_aux_date)]
        # 衰退期：右辅助线之后
        decline_data = valid_data[valid_data['日期_日'] > right_aux_date]

        print(f"潜伏期数据量: {len(latent_data)}")
        print(f"爆发期数据量: {len(outbreak_data)}")
        print(f"衰退期数据量: {len(decline_data)}")

        # 统计各时期各用户类别的数量
        periods = ['潜伏期', '爆发期', '衰退期']
        period_data = [latent_data, outbreak_data, decline_data]

        # 创建数据矩阵
        data_matrix = []
        for period_df in period_data:
            period_counts = []
            total_count = len(period_df)
            for category in user_categories:
                count = len(period_df[period_df['用户划分'] == category])
                percentage = (count / total_count * 100) if total_count > 0 else 0
                period_counts.append(percentage)
            data_matrix.append(period_counts)

        # 转置矩阵，使每行代表一个用户类别
        data_matrix = list(map(list, zip(*data_matrix)))

        # 创建堆叠柱状图
        fig, ax = plt.subplots(figsize=(12, 8))

        # 设置颜色
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2']

        # 绘制堆叠柱状图
        bottom = [0] * len(periods)
        bars = []

        for i, category in enumerate(user_categories):
            color = colors[i % len(colors)]
            bar = ax.bar(periods, data_matrix[i], bottom=bottom,
                        label=category, color=color, alpha=0.8)
            bars.append(bar)

            # 更新底部位置
            bottom = [bottom[j] + data_matrix[i][j] for j in range(len(periods))]

            # 在柱状图上添加百分比标签
            for j, (period, value) in enumerate(zip(periods, data_matrix[i])):
                if value > 2:  # 只显示大于2%的标签，避免太小的标签重叠
                    height = bottom[j] - value/2  # 标签位置在该段的中间
                    ax.text(j, height, f'{value:.1f}%',
                           ha='center', va='center', fontsize=10,
                           color='white', weight='bold')

        # 设置图表属性
        ax.set_title('各时期用户类型分布（堆叠柱状图）', fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel('时期', fontsize=12)
        ax.set_ylabel('用户比例 (%)', fontsize=12)
        ax.legend(loc='upper right', fontsize=10)
        ax.set_ylim(0, 100)

        # 添加网格
        ax.grid(True, alpha=0.3, axis='y')

        # 在每个柱子上方显示总数
        for j, period in enumerate(periods):
            total = len(period_data[j])
            ax.text(j, 102, f'总计: {total}条',
                   ha='center', va='bottom', fontsize=11, weight='bold')

        # 调整布局
        plt.tight_layout()

        # 保存图片
        plt.savefig('143广州一商贩持铁棍追打城管用户类型时期分布堆叠图.png', dpi=300, bbox_inches='tight')
        print("堆叠柱状图已保存为 '143广州一商贩持铁棍追打城管用户类型时期分布堆叠图.png'")

        # 显示详细统计
        print(f"\n=== 各时期用户类型分布统计 ===")
        for i, period in enumerate(periods):
            print(f"\n{period}:")
            total = len(period_data[i])
            print(f"  总计: {total} 条")
            for j, category in enumerate(user_categories):
                count = len(period_data[i][period_data[i]['用户划分'] == category])
                percentage = (count / total * 100) if total > 0 else 0
                print(f"  {category}: {count} 条 ({percentage:.1f}%)")

        plt.close()
    else:
        print("未找到有效的辅助线点，无法创建堆叠柱状图")

def create_user_category_timeline(df):
    """创建用户划分时间线图表"""
    print("开始创建用户划分时间线图表...")

    # 获取所有非空的用户划分类别（过滤掉空字符串和空白字符串）
    user_categories = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')]['用户划分'].unique()
    # 进一步过滤掉只包含空白字符的类别
    user_categories = [cat for cat in user_categories if cat.strip() != '']
    print(f"用户划分类别: {user_categories}")
    print(f"用户划分类别数量: {len(user_categories)}")

    if len(user_categories) == 0:
        print("没有有效的用户划分数据，无法绘制图表")
        return

    # 只使用有用户划分的数据（过滤掉空字符串和空白字符串）
    valid_data = df[df['用户划分'].notna() & (df['用户划分'].str.strip() != '')].copy()
    print(f"有效数据量: {len(valid_data)}")

    # 按日期和用户划分分组统计
    daily_user_counts = valid_data.groupby(['日期_日', '用户划分']).size().reset_index(name='数量')

    # 创建完整的日期范围
    full_date_range = pd.date_range(
        start=valid_data['日期_日'].min(),
        end=valid_data['日期_日'].max(),
        freq='D'
    ).date

    # 为每个用户类别创建完整的时间序列
    all_data = []
    for category in user_categories:
        category_data = daily_user_counts[daily_user_counts['用户划分'] == category]

        # 创建完整日期序列，缺失日期填充为0
        full_series = pd.DataFrame({'日期_日': full_date_range})
        full_series = full_series.merge(category_data[['日期_日', '数量']], on='日期_日', how='left')
        full_series['数量'] = full_series['数量'].fillna(0)
        full_series['用户划分'] = category

        all_data.append(full_series)

    # 合并所有数据
    plot_data = pd.concat(all_data, ignore_index=True)

    # 优化日期范围：删除本身为0且前后一天也都为0的时间段
    print("正在优化日期范围...")

    # 按日期计算每天所有用户类别的总数
    daily_totals = plot_data.groupby('日期_日')['数量'].sum().reset_index()
    daily_totals = daily_totals.sort_values('日期_日').reset_index(drop=True)

    # 标记需要保留的日期
    keep_dates = []

    for i in range(len(daily_totals)):
        current_total = daily_totals.iloc[i]['数量']
        current_date = daily_totals.iloc[i]['日期_日']

        # 获取前一天和后一天的总数
        prev_total = daily_totals.iloc[i-1]['数量'] if i > 0 else 0
        next_total = daily_totals.iloc[i+1]['数量'] if i < len(daily_totals)-1 else 0

        # 如果当前天为0，且前一天和后一天也都为0，则不保留
        if current_total <50 and prev_total < 50 and next_total < 50:
            continue  # 跳过这个日期
        else:
            keep_dates.append(current_date)

    if len(keep_dates) > 0:
        # 过滤数据到优化后的日期范围
        plot_data = plot_data[plot_data['日期_日'].isin(keep_dates)]

        print(f"原始日期范围: {len(full_date_range)} 天")
        print(f"优化后日期范围: {len(keep_dates)} 天")
        print(f"删除了 {len(full_date_range) - len(keep_dates)} 天的无效数据")
        print(f"优化后时间范围: {min(keep_dates)} 至 {max(keep_dates)}")
    else:
        print("所有日期的数据都为0，无法优化日期范围")
        return

    # 计算总计线数据
    total_data = plot_data.groupby('日期_日')['数量'].sum().reset_index()
    total_data = total_data.sort_values('日期_日')

    # 找到峰值和辅助线点
    auxiliary_points = find_auxiliary_points(total_data)

    # 绘制图表
    plt.figure(figsize=(16, 10))  # 增大图表尺寸以容纳标签

    # 为每个用户类别定义颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']

    # 先绘制总计线（使用粗线和特殊样式）
    plt.plot(total_data['日期_日'], total_data['数量'],
            marker='s', linewidth=4, markersize=8,
            color='black', label='总计', linestyle='-', alpha=0.8)

    # 为总计线添加标签
    for idx, row in total_data.iterrows():
        x_val = row['日期_日']
        y_val = row['数量']

        date_str = x_val.strftime('%m-%d')
        label_text = f'({date_str}, {int(y_val)})'

        # 总计线标签放在最上方，使用更大的字体
        plt.annotate(label_text,
                   (x_val, y_val),
                   xytext=(0, 20),  # 向上偏移更多
                   textcoords='offset points',
                   fontsize=14,  # 更大的字体
                   color='black',
                   alpha=1.0,
                   bbox=dict(boxstyle='round,pad=0.4',
                           facecolor='yellow',  # 黄色背景突出显示
                           edgecolor='black',
                           alpha=0.9),
                   ha='center',
                   weight='bold')

    # 然后绘制各个用户类别的线条
    for i, category in enumerate(user_categories):
        if category == '':
            continue

        category_data = plot_data[plot_data['用户划分'] == category]
        color = colors[i % len(colors)]

        # 绘制线条和点
        plt.plot(category_data['日期_日'], category_data['数量'],
                marker='o', linewidth=2, markersize=5,
                color=color, label=category)

        # 为每个点添加坐标标签
        for idx, row in category_data.iterrows():
            x_val = row['日期_日']
            y_val = row['数量']

            # 为所有点添加标签，显示完整坐标信息
            # 格式化日期显示（只显示月-日）
            date_str = x_val.strftime('%m-%d')
            label_text = f'({date_str}, {int(y_val)})'

            # 根据y值调整标签位置，避免重叠
            if y_val == 0:
                # 0值点的标签放在下方
                xytext = (0, -25)
                fontsize = 11
                alpha = 0.8
            else:
                # 非0值点的标签放在上方
                xytext = (5, 12)
                fontsize = 13
                alpha = 0.9

            # 添加文本标签
            plt.annotate(label_text,
                       (x_val, y_val),
                       xytext=xytext,
                       textcoords='offset points',
                       fontsize=fontsize,
                       color=color,
                       alpha=alpha,
                       bbox=dict(boxstyle='round,pad=0.3',
                               facecolor='white',
                               edgecolor=color,
                               alpha=0.8),
                       ha='center')

    # 绘制辅助线和时期标注
    if auxiliary_points['left_aux_idx'] is not None and auxiliary_points['right_aux_idx'] is not None:
        left_aux_date = auxiliary_points['left_aux_date']
        right_aux_date = auxiliary_points['right_aux_date']

        # 绘制辅助线
        plt.axvline(x=left_aux_date, color='red', linestyle='--', linewidth=2, alpha=0.7, label='左侧辅助线')
        plt.axvline(x=right_aux_date, color='blue', linestyle='--', linewidth=2, alpha=0.7, label='右侧辅助线')

        # 获取x轴范围和y轴最大值
        y_max = plt.ylim()[1]

        # 获取实际的日期范围
        date_range = plot_data['日期_日'].unique()
        date_range = sorted(date_range)
        x_min_date = date_range[0]
        x_max_date = date_range[-1]

        # 计算三个时期的中心位置（使用日期计算）
        # 潜伏期：从最小日期到左辅助线
        latent_days = (left_aux_date - x_min_date).days
        latent_center = x_min_date + pd.Timedelta(days=latent_days // 2)

        # 爆发期：从左辅助线到右辅助线
        outbreak_days = (right_aux_date - left_aux_date).days
        outbreak_center = left_aux_date + pd.Timedelta(days=outbreak_days // 2)

        # 衰退期：从右辅助线到最大日期
        decline_days = (x_max_date - right_aux_date).days
        decline_center = right_aux_date + pd.Timedelta(days=decline_days // 2)

        # 添加时期背景色块
        plt.axvspan(x_min_date, left_aux_date, alpha=0.1, color='green', label='潜伏期')
        plt.axvspan(left_aux_date, right_aux_date, alpha=0.1, color='orange', label='爆发期')
        plt.axvspan(right_aux_date, x_max_date, alpha=0.1, color='purple', label='衰退期')

        # 添加时期标注
        plt.text(latent_center, y_max * 0.95, '潜伏期',
                ha='center', va='top', fontsize=16, color='green', weight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', edgecolor='green', alpha=0.9))

        plt.text(outbreak_center, y_max * 0.95, '爆发期',
                ha='center', va='top', fontsize=16, color='orange', weight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', edgecolor='orange', alpha=0.9))

        plt.text(decline_center, y_max * 0.95, '衰退期',
                ha='center', va='top', fontsize=16, color='purple', weight='bold',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', edgecolor='purple', alpha=0.9))

        # 在辅助线上添加日期标注
        plt.text(left_aux_date, y_max * 0.85, f'左辅助线\n{left_aux_date.strftime("%m-%d")}',
                ha='center', va='top', fontsize=11, color='red', weight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='red', alpha=0.8))

        plt.text(right_aux_date, y_max * 0.85, f'右辅助线\n{right_aux_date.strftime("%m-%d")}',
                ha='center', va='top', fontsize=11, color='blue', weight='bold',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', edgecolor='blue', alpha=0.8))

    # 设置图表属性
    plt.title('各类用户随时间变化趋势', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('用户数量（条）', fontsize=12)
    plt.legend(loc='upper right', fontsize=10)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)

    # 自动调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig('用户划分时间线图.png', dpi=300, bbox_inches='tight')
    print("图表已保存为 '用户划分时间线图.png'")

    # 关闭图表以释放内存
    plt.close()

    # 打印统计信息
    print(f"\n统计信息:")
    optimized_date_range = plot_data['日期_日'].unique()
    print(f"优化后时间范围: {min(optimized_date_range)} 至 {max(optimized_date_range)}")
    print(f"优化后总天数: {len(optimized_date_range)}")

    for category in user_categories:
        total_count = valid_data[valid_data['用户划分'] == category].shape[0]
        print(f"{category}: 总计 {total_count} 条")

    # 返回辅助线点信息，供堆叠柱状图使用
    return auxiliary_points

def main():
    """主函数"""
    print("开始处理数据...")

    # 读取数据
    df = load_data()
    if df is None:
        print("数据读取失败，程序退出")
        return

    print(f"数据形状: {df.shape}")

    # 检查是否有用户划分列
    if '用户划分' not in df.columns:
        print("错误：数据中没有'用户划分'列")
        return

    # 处理日期
    df_processed = process_date_column(df)
    print(f"处理后数据形状: {df_processed.shape}")

    # 创建图表并获取辅助线点信息
    auxiliary_points = create_user_category_timeline(df_processed)

    # 创建堆叠柱状图
    if auxiliary_points:
        create_stacked_bar_chart(df_processed, auxiliary_points)

    print("处理完成！")

if __name__ == "__main__":
    main()
