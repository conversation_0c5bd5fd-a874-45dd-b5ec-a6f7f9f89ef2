import pandas as pd
from ltp import LTP
import os
import requests
import json

# 定义强势群体和弱势群体
strong_groups = {"城管", "警察", "老师", "教官", "政府", "保安", "医生", "法官", "班主任"}
weak_groups = {"学生", "小贩", "摊贩", "市民", "孩子", "女孩", "老人", "男童", "女童", "百姓", "居民"}

# AI配置选项
AI_CONFIG = {
    "use_ai": True,  # 是否使用AI辅助
    "ai_service": "local",  # 可选: "openai", "local", "ollama"
    "api_key": "",  # OpenAI API密钥
    "api_url": "http://localhost:11434/api/generate",  # 本地AI服务URL (如Ollama)
    "model": "qwen:7b"  # 使用的模型
}

def call_ai_for_svo(title):
    """
    调用AI来分析主谓宾
    """
    if not AI_CONFIG["use_ai"]:
        return None
    
    if AI_CONFIG["ai_service"] == "local" or AI_CONFIG["ai_service"] == "ollama":
        return call_local_ai(title)
    elif AI_CONFIG["ai_service"] == "openai":
        return call_openai_api(title)
    else:
        return None

def call_local_ai(title):
    """
    调用本地AI服务 (如Ollama)
    """
    prompt = f"""请分析以下中文新闻标题的主谓宾结构，要求主语和宾语必须是人：

标题：{title}

请直接回答，格式如下：
主语：[必须是人物，如城管、商贩、老人、学生等，如果没有人物主语则填"无"]
谓语：[动词]
宾语：[必须是人物，如城管、商贩、老人、学生等，如果没有人物宾语则填"无"]

重要要求：
1. 主语和宾语只能是人，不能是物品、地点或抽象概念
2. 如果句子中没有明确的人物作为主语或宾语，请填写"无"
3. 优先识别具体的人物身份，如：城管、商贩、老人、学生、警察等"""

    try:
        data = {
            "model": AI_CONFIG["model"],
            "prompt": prompt,
            "stream": False
        }
        
        response = requests.post(AI_CONFIG["api_url"], json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result.get('response', '')
            return parse_ai_response(content)
        else:
            print(f"本地AI调用失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"本地AI调用出错: {e}")
        return None

def call_openai_api(title):
    """
    调用OpenAI API
    """
    if not AI_CONFIG["api_key"]:
        print("请设置OpenAI API密钥")
        return None

    prompt = f"""请分析以下中文新闻标题的主谓宾结构，要求主语和宾语必须是人：

标题：{title}

请按照以下格式回答：
主语：[必须是人物，如城管、商贩、老人等，如果没有人物主语则填"无"]
谓语：[动词]
宾语：[必须是人物，如城管、商贩、老人等，如果没有人物宾语则填"无"]

重要要求：主语和宾语只能是人，不能是物品、地点或抽象概念。"""

    headers = {
        "Authorization": f"Bearer {AI_CONFIG['api_key']}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "gpt-3.5-turbo",
        "messages": [{"role": "user", "content": prompt}],
        "temperature": 0.1,
        "max_tokens": 200
    }
    
    try:
        response = requests.post("https://api.openai.com/v1/chat/completions", 
                               headers=headers, json=data, timeout=30)
        if response.status_code == 200:
            result = response.json()
            content = result['choices'][0]['message']['content']
            return parse_ai_response(content)
        else:
            print(f"OpenAI API调用失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"OpenAI API调用出错: {e}")
        return None

def parse_ai_response(content):
    """
    解析AI响应，提取主谓宾
    """
    try:
        lines = content.strip().split('\n')
        result = {}
        
        for line in lines:
            line = line.strip()
            if line.startswith('主语：') or line.startswith('主语:'):
                result['subject'] = line.split('：')[-1].split(':')[-1].strip()
            elif line.startswith('谓语：') or line.startswith('谓语:'):
                result['predicate'] = line.split('：')[-1].split(':')[-1].strip()
            elif line.startswith('宾语：') or line.startswith('宾语:'):
                result['object'] = line.split('：')[-1].split(':')[-1].strip()
        
        # 清理结果
        for key in result:
            result[key] = result[key].replace('[', '').replace(']', '').strip()
        
        return result if len(result) >= 2 else None
    except Exception as e:
        print(f"解析AI响应出错: {e}")
        return None

def extract_svo_with_ai(title, words, pos_tags, dependencies):
    """
    使用AI辅助提取主谓宾
    """
    # 尝试使用AI
    ai_result = call_ai_for_svo(title)
    
    if ai_result and 'subject' in ai_result and 'predicate' in ai_result:
        subject = ai_result.get('subject', '未识别')
        predicate = ai_result.get('predicate', '未识别')
        object_word = ai_result.get('object', '未识别')
        
        print(f"   AI分析: 主语={subject}, 谓语={predicate}, 宾语={object_word}")
        return subject, predicate, object_word
    else:
        # AI失败时使用LTP
        print("   AI分析失败，使用LTP")
        return extract_svo_fallback(words, pos_tags, dependencies)

def extract_svo_fallback(words, pos_tags, dependencies):
    """
    LTP备用方案 - 只提取人物作为主语和宾语
    """
    subject = "无"
    predicate = "未识别"
    object_word = "无"

    # 定义人物相关词汇
    person_indicators = {
        '城管', '警察', '老师', '教官', '政府', '保安', '医生', '法官', '班主任',
        '学生', '小贩', '摊贩', '市民', '孩子', '女孩', '老人', '男童', '女童',
        '百姓', '居民', '商贩', '店主', '男子', '女子', '大爷', '大妈', '师傅',
        '人', '员工', '工人', '农民', '司机', '乘客', '顾客', '患者', '家长'
    }

    # 寻找动词作为谓语
    predicate_idx = -1
    for i, pos in enumerate(pos_tags):
        if pos.startswith('v'):
            predicate = words[i]
            predicate_idx = i
            break

    # 寻找人物主语（在谓语前）
    if predicate_idx != -1:
        for j in range(predicate_idx-1, -1, -1):
            word = words[j]
            # 检查是否是人物
            if (any(person in word for person in person_indicators) or
                word in person_indicators or
                (pos_tags[j].startswith('n') and is_person_word(word))):
                # 扩展主语，包含修饰词
                subject = expand_person_entity(words, j, "left")
                break

    # 寻找人物宾语（在谓语后）
    if predicate_idx != -1:
        for j in range(predicate_idx+1, len(words)):
            word = words[j]
            # 检查是否是人物
            if (any(person in word for person in person_indicators) or
                word in person_indicators or
                (pos_tags[j].startswith('n') and is_person_word(word))):
                # 扩展宾语，包含修饰词
                object_word = expand_person_entity(words, j, "right")
                break

    return subject, predicate, object_word

def is_person_word(word):
    """
    判断词语是否可能指代人物
    """
    person_suffixes = ['人', '者', '员', '师', '长', '手', '工', '民', '客', '户']
    person_prefixes = ['老', '小', '大']

    # 检查后缀
    if any(word.endswith(suffix) for suffix in person_suffixes):
        return True

    # 检查前缀
    if any(word.startswith(prefix) for prefix in person_prefixes) and len(word) > 1:
        return True

    return False

def expand_person_entity(words, person_idx, direction):
    """
    扩展人物实体，包含修饰词
    """
    entity_words = [words[person_idx]]

    if direction == "left":
        # 向左扩展修饰词
        for i in range(person_idx-1, -1, -1):
            word = words[i]
            if word in ['一', '某', '该', '这', '那', '两', '三', '多'] or len(word) <= 2:
                entity_words.insert(0, word)
            else:
                break
    elif direction == "right":
        # 向右扩展修饰词
        for i in range(person_idx+1, len(words)):
            word = words[i]
            if len(word) <= 2 and word not in ['的', '了', '着', '过']:
                entity_words.append(word)
            else:
                break

    return "".join(entity_words)

def analyze_groups(words, subject, object_word):
    """
    分析群体类型
    """
    all_text = ' '.join(words) + ' ' + subject + ' ' + object_word
    
    strong_found = [group for group in strong_groups if group in all_text]
    weak_found = [group for group in weak_groups if group in all_text]
    
    # 分析主语和宾语的群体类型
    subject_type = "未知"
    object_type = "未知"
    
    for group in strong_groups:
        if group in subject:
            subject_type = "强势群体"
            break
    if subject_type == "未知":
        for group in weak_groups:
            if group in subject:
                subject_type = "弱势群体"
                break
    
    for group in strong_groups:
        if group in object_word:
            object_type = "强势群体"
            break
    if object_type == "未知":
        for group in weak_groups:
            if group in object_word:
                object_type = "弱势群体"
                break
    
    # 分析群体关系
    relationship = "未明确"
    if subject_type == "强势群体" and object_type == "弱势群体":
        relationship = "强势群体对弱势群体"
    elif subject_type == "弱势群体" and object_type == "强势群体":
        relationship = "弱势群体对强势群体"
    elif subject_type == "强势群体" and object_type == "强势群体":
        relationship = "强势群体之间"
    elif subject_type == "弱势群体" and object_type == "弱势群体":
        relationship = "弱势群体之间"
    elif subject_type == "强势群体" or object_type == "强势群体":
        relationship = "涉及强势群体"
    elif subject_type == "弱势群体" or object_type == "弱势群体":
        relationship = "涉及弱势群体"
    
    return {
        'subject_type': subject_type,
        'object_type': object_type,
        'strong_groups_found': ', '.join(strong_found) if strong_found else "无",
        'weak_groups_found': ', '.join(weak_found) if weak_found else "无",
        'relationship': relationship
    }

def analyze_news_titles():
    """
    分析新闻标题
    """
    print("正在初始化LTP模型...")
    ltp = LTP()
    
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    file_path = os.path.join(desktop_path, "事件.xlsx")
    
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件，共 {len(df)} 条数据")
        
        if AI_CONFIG["use_ai"]:
            print(f"使用AI服务: {AI_CONFIG['ai_service']}")
        else:
            print("仅使用LTP分析")
            
    except FileNotFoundError:
        print(f"未找到文件: {file_path}")
        return
    
    title_column = df.columns[0]
    titles = df[title_column].dropna().astype(str)
    
    results = []
    
    for idx, title in enumerate(titles):
        print(f"\n正在分析第 {idx+1}/{len(titles)} 条: {title[:30]}...")
        
        try:
            # LTP分析
            result = ltp.pipeline([title], tasks=["cws", "pos", "dep"])
            words = result.cws[0]
            pos_tags = result.pos[0]
            dependencies = list(zip(result.dep[0]['head'], result.dep[0]['label']))
            
            # AI辅助主谓宾分析
            subject, predicate, object_word = extract_svo_with_ai(title, words, pos_tags, dependencies)
            
            # 群体分析
            group_analysis = analyze_groups(words, subject, object_word)
            
            result_data = {
                '原标题': title,
                '分词结果': ' / '.join(words),
                '主语': subject,
                '谓语': predicate,
                '宾语': object_word,
                '主语群体类型': group_analysis['subject_type'],
                '宾语群体类型': group_analysis['object_type'],
                '涉及强势群体': group_analysis['strong_groups_found'],
                '涉及弱势群体': group_analysis['weak_groups_found'],
                '群体关系': group_analysis['relationship'],
                '完整分析': ' | '.join([f"{w}({p})" for w, p in zip(words, pos_tags)])
            }
            results.append(result_data)
            
        except Exception as e:
            print(f"分析出错: {e}")
            results.append({
                '原标题': title,
                '分词结果': '分析失败',
                '主语': '分析失败',
                '谓语': '分析失败',
                '宾语': '分析失败',
                '主语群体类型': '分析失败',
                '宾语群体类型': '分析失败',
                '涉及强势群体': '分析失败',
                '涉及弱势群体': '分析失败',
                '群体关系': '分析失败',
                '完整分析': f'错误: {e}'
            })
    
    # 保存结果
    result_df = pd.DataFrame(results)
    final_df = pd.concat([df.iloc[:len(results)], result_df], axis=1)
    
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(desktop_path, f"事件_AI分析结果_{timestamp}.xlsx")
    final_df.to_excel(output_path, index=False)
    
    print(f"\n分析完成！结果已保存到: {output_path}")
    
    # 显示前几条结果
    print("\n前3条分析结果预览:")
    for i, result in enumerate(results[:3]):
        print(f"\n{i+1}. {result['原标题']}")
        print(f"   主语: {result['主语']} ({result['主语群体类型']})")
        print(f"   谓语: {result['谓语']}")
        print(f"   宾语: {result['宾语']} ({result['宾语群体类型']})")
        print(f"   群体关系: {result['群体关系']}")

if __name__ == "__main__":
    # 配置AI服务
    print("=== AI辅助主谓宾分析系统 ===")
    print("1. 不使用AI，仅LTP")
    print("2. 使用本地AI (需要Ollama等)")
    print("3. 使用OpenAI API")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == "1":
        AI_CONFIG["use_ai"] = False
    elif choice == "2":
        AI_CONFIG["ai_service"] = "local"
        model = input("输入模型名称 (默认qwen:7b): ").strip()
        if model:
            AI_CONFIG["model"] = model
    elif choice == "3":
        AI_CONFIG["ai_service"] = "openai"
        api_key = input("输入OpenAI API密钥: ").strip()
        AI_CONFIG["api_key"] = api_key
    
    analyze_news_titles()
