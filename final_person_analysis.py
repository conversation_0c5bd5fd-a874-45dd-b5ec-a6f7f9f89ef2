import pandas as pd
from ltp import LTP
import os
import datetime

# 定义强势群体和弱势群体
strong_groups = {"城管", "警察", "老师", "教官", "政府", "保安", "医生", "法官", "班主任"}
weak_groups = {"学生", "小贩", "摊贩", "市民", "孩子", "女孩", "老人", "男童", "女童", "百姓", "居民"}

def analyze_news_with_person_focus():
    """
    专注于人物的新闻标题分析
    """
    print("=== 人物主谓宾分析系统 ===")
    print("要求：主语和宾语必须是人")
    
    # 读取数据
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    file_path = os.path.join(desktop_path, "事件.xlsx")
    
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件，共 {len(df)} 条数据")
    except FileNotFoundError:
        print(f"未找到文件: {file_path}")
        return
    
    print("正在初始化LTP模型...")
    ltp = LTP()
    print("LTP初始化完成")
    
    title_column = df.columns[0]
    titles = df[title_column].dropna().astype(str)
    
    results = []
    
    for idx, title in enumerate(titles):
        print(f"\n正在分析第 {idx+1}/{len(titles)} 条:")
        print(f"标题: {title}")
        
        try:
            # LTP分析
            result = ltp.pipeline([title], tasks=["cws", "pos", "dep"])
            words = result.cws[0]
            pos_tags = result.pos[0]
            dependencies = list(zip(result.dep[0]['head'], result.dep[0]['label']))
            
            print(f"分词: {' / '.join(words)}")
            
            # 提取人物主谓宾
            subject, predicate, object_word = extract_person_svo_improved(words, pos_tags, dependencies)
            
            print(f"主语(人物): {subject}")
            print(f"谓语: {predicate}")
            print(f"宾语(人物): {object_word}")
            
            # 群体分析
            group_analysis = analyze_person_groups(words, subject, object_word)
            print(f"群体关系: {group_analysis['relationship']}")
            
            result_data = {
                '原标题': title,
                '分词结果': ' / '.join(words),
                '主语(人物)': subject,
                '谓语': predicate,
                '宾语(人物)': object_word,
                '主语群体类型': group_analysis['subject_type'],
                '宾语群体类型': group_analysis['object_type'],
                '涉及强势群体': group_analysis['strong_groups_found'],
                '涉及弱势群体': group_analysis['weak_groups_found'],
                '群体关系': group_analysis['relationship']
            }
            results.append(result_data)
            
        except Exception as e:
            print(f"分析出错: {e}")
            result_data = {
                '原标题': title,
                '分词结果': '分析失败',
                '主语(人物)': '分析失败',
                '谓语': '分析失败',
                '宾语(人物)': '分析失败',
                '主语群体类型': '分析失败',
                '宾语群体类型': '分析失败',
                '涉及强势群体': '分析失败',
                '涉及弱势群体': '分析失败',
                '群体关系': '分析失败'
            }
            results.append(result_data)
    
    # 保存结果
    result_df = pd.DataFrame(results)
    final_df = pd.concat([df.iloc[:len(results)], result_df], axis=1)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(desktop_path, f"事件_人物主谓宾分析_{timestamp}.xlsx")
    final_df.to_excel(output_path, index=False)
    
    print(f"\n=== 分析完成 ===")
    print(f"结果已保存到: {output_path}")
    
    # 统计分析
    print_statistics(results)

def extract_person_svo_improved(words, pos_tags, dependencies):
    """
    改进的人物主谓宾提取
    """
    # 人物关键词库
    person_keywords = {
        '城管', '警察', '老师', '教官', '政府', '保安', '医生', '法官', '班主任',
        '学生', '小贩', '摊贩', '市民', '孩子', '女孩', '老人', '男童', '女童', 
        '百姓', '居民', '商贩', '店主', '男子', '女子', '大爷', '大妈', '师傅',
        '人', '商户', '摊主', '贩子', '小商贩'
    }
    
    subject = "无"
    predicate = "未识别"
    object_word = "无"
    
    # 寻找主要动词
    verb_indices = []
    for i, pos in enumerate(pos_tags):
        if pos.startswith('v'):
            verb_indices.append(i)
    
    if verb_indices:
        # 选择最重要的动词（通常是第一个或根节点）
        predicate_idx = verb_indices[0]
        predicate = words[predicate_idx]
        
        # 寻找人物主语（在动词前）
        for i in range(predicate_idx-1, -1, -1):
            if is_person_entity(words[i], person_keywords):
                subject = build_person_entity(words, i, "backward")
                break
        
        # 寻找人物宾语（在动词后）
        for i in range(predicate_idx+1, len(words)):
            if is_person_entity(words[i], person_keywords):
                object_word = build_person_entity(words, i, "forward")
                break
    
    return subject, predicate, object_word

def is_person_entity(word, person_keywords):
    """
    判断词语是否是人物实体
    """
    # 直接匹配
    if word in person_keywords:
        return True
    
    # 包含人物关键词
    if any(keyword in word for keyword in person_keywords):
        return True
    
    # 人物词汇模式
    person_suffixes = ['人', '者', '员', '师', '长', '手', '工', '民', '客', '户', '贩']
    if any(word.endswith(suffix) for suffix in person_suffixes) and len(word) > 1:
        return True
    
    # 特殊前缀
    if word.startswith('老') and len(word) > 1:
        return True
    if word.startswith('小') and len(word) > 1 and word not in ['小区', '小车', '小店', '小时']:
        return True
    
    return False

def build_person_entity(words, person_idx, direction):
    """
    构建完整的人物实体
    """
    entity_parts = [words[person_idx]]
    
    if direction == "backward":
        # 向前收集修饰词
        for i in range(person_idx-1, -1, -1):
            word = words[i]
            if (word in ['一', '某', '该', '这', '那', '两', '三', '多', '几'] or 
                is_location_word(word) or 
                (len(word) <= 3 and not word in ['被', '把', '将', '给', '对', '向'])):
                entity_parts.insert(0, word)
            else:
                break
    elif direction == "forward":
        # 向后收集修饰词（较少使用）
        for i in range(person_idx+1, len(words)):
            word = words[i]
            if len(word) <= 2 and word not in ['的', '了', '着', '过', '被', '把', '将']:
                entity_parts.append(word)
            else:
                break
    
    return "".join(entity_parts)

def is_location_word(word):
    """
    判断是否是地点词
    """
    locations = ['广州', '北京', '上海', '深圳', '杭州', '南京', '武汉', '成都', 
                '重庆', '天津', '西安', '沈阳', '大连', '青岛', '厦门', '苏州',
                '辽宁', '山东', '江苏', '浙江', '广东', '湖北', '湖南', '河南',
                '河北', '山西', '陕西', '四川', '云南', '贵州', '福建', '安徽',
                '宁波', '合肥', '邢台', '孝感', '衡阳', '太原', '东莞', '南江']
    return any(loc in word for loc in locations)

def analyze_person_groups(words, subject, object_word):
    """
    分析人物群体类型
    """
    all_text = ' '.join(words) + ' ' + subject + ' ' + object_word
    
    strong_found = [group for group in strong_groups if group in all_text]
    weak_found = [group for group in weak_groups if group in all_text]
    
    # 分析主语群体类型
    subject_type = "无人物"
    if subject != "无":
        if any(group in subject for group in strong_groups):
            subject_type = "强势群体"
        elif any(group in subject for group in weak_groups):
            subject_type = "弱势群体"
        else:
            subject_type = "普通人物"
    
    # 分析宾语群体类型
    object_type = "无人物"
    if object_word != "无":
        if any(group in object_word for group in strong_groups):
            object_type = "强势群体"
        elif any(group in object_word for group in weak_groups):
            object_type = "弱势群体"
        else:
            object_type = "普通人物"
    
    # 分析群体关系
    relationship = "无人物关系"
    if subject != "无" and object_word != "无":
        if subject_type == "强势群体" and object_type == "弱势群体":
            relationship = "强势群体对弱势群体"
        elif subject_type == "弱势群体" and object_type == "强势群体":
            relationship = "弱势群体对强势群体"
        elif subject_type == "强势群体" and object_type == "强势群体":
            relationship = "强势群体之间"
        elif subject_type == "弱势群体" and object_type == "弱势群体":
            relationship = "弱势群体之间"
        else:
            relationship = "人物之间"
    elif subject != "无":
        if subject_type == "强势群体":
            relationship = "涉及强势群体"
        elif subject_type == "弱势群体":
            relationship = "涉及弱势群体"
        else:
            relationship = "涉及人物"
    elif object_word != "无":
        if object_type == "强势群体":
            relationship = "涉及强势群体"
        elif object_type == "弱势群体":
            relationship = "涉及弱势群体"
        else:
            relationship = "涉及人物"
    
    return {
        'subject_type': subject_type,
        'object_type': object_type,
        'strong_groups_found': ', '.join(strong_found) if strong_found else "无",
        'weak_groups_found': ', '.join(weak_found) if weak_found else "无",
        'relationship': relationship
    }

def print_statistics(results):
    """
    打印统计信息
    """
    print("\n=== 统计分析 ===")
    
    total = len(results)
    has_subject = sum(1 for r in results if r['主语(人物)'] != '无' and r['主语(人物)'] != '分析失败')
    has_object = sum(1 for r in results if r['宾语(人物)'] != '无' and r['宾语(人物)'] != '分析失败')
    has_both = sum(1 for r in results if r['主语(人物)'] != '无' and r['宾语(人物)'] != '无' and 
                   r['主语(人物)'] != '分析失败' and r['宾语(人物)'] != '分析失败')
    
    print(f"总标题数: {total}")
    print(f"有人物主语: {has_subject} ({has_subject/total*100:.1f}%)")
    print(f"有人物宾语: {has_object} ({has_object/total*100:.1f}%)")
    print(f"主语宾语都是人物: {has_both} ({has_both/total*100:.1f}%)")
    
    # 群体关系统计
    relationship_counts = {}
    for r in results:
        rel = r['群体关系']
        if rel != '分析失败':
            relationship_counts[rel] = relationship_counts.get(rel, 0) + 1
    
    print("\n群体关系分布:")
    for relation, count in sorted(relationship_counts.items()):
        print(f"  {relation}: {count}条")

if __name__ == "__main__":
    analyze_news_with_person_focus()
