import pandas as pd
from ltp import LTP
import os

# 定义强势群体和弱势群体
strong_groups = {"城管", "警察", "老师", "教官", "政府", "保安", "医生", "法官", "班主任"}
weak_groups = {"学生", "小贩", "摊贩", "市民", "孩子", "女孩", "老人", "男童", "女童", "百姓", "居民"}

def analyze_news_titles():
    """
    分析新闻标题的主谓宾结构
    """
    # 初始化LTP模型
    print("正在初始化LTP模型...")
    ltp = LTP()
    
    # 读取桌面上的"事件"表格
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    file_path = os.path.join(desktop_path, "事件.xlsx")
    
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(file_path)
        print(f"成功读取文件: {file_path}")
        print(f"数据形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
        
    except FileNotFoundError:
        print(f"未找到文件: {file_path}")
        print("请确保桌面上有名为'事件.xlsx'的文件")
        return
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return
    
    # 获取第一列（新闻标题）
    if df.empty:
        print("表格为空")
        return
    
    # 假设第一列是新闻标题
    title_column = df.columns[0]
    titles = df[title_column].dropna().astype(str)
    
    print(f"开始分析 {len(titles)} 条新闻标题...")
    
    # 存储分析结果
    results = []
    
    for idx, title in enumerate(titles):
        print(f"正在分析第 {idx+1}/{len(titles)} 条: {title[:50]}...")
        
        try:
            # 使用LTP进行分词和依存句法分析
            result = ltp.pipeline([title], tasks=["cws", "pos", "dep"])

            # 提取词语、词性和依存关系
            words = result.cws[0]
            pos_tags = result.pos[0]
            dependencies = list(zip(result.dep[0]['head'], result.dep[0]['label']))
            
            # 分析主谓宾结构
            subject, predicate, object_word = extract_svo(words, pos_tags, dependencies)

            # 分析群体类型
            group_analysis = analyze_groups(words, subject, object_word)

            # 存储结果
            result = {
                '原标题': title,
                '分词结果': ' / '.join(words),
                '主语': subject,
                '谓语': predicate,
                '宾语': object_word,
                '主语群体类型': group_analysis['subject_type'],
                '宾语群体类型': group_analysis['object_type'],
                '涉及强势群体': group_analysis['strong_groups_found'],
                '涉及弱势群体': group_analysis['weak_groups_found'],
                '群体关系': group_analysis['relationship'],
                '完整分析': format_detailed_analysis(words, pos_tags, dependencies)
            }
            results.append(result)
            
        except Exception as e:
            print(f"分析标题时出错: {title[:50]}... 错误: {e}")
            result = {
                '原标题': title,
                '分词结果': '分析失败',
                '主语': '分析失败',
                '谓语': '分析失败',
                '宾语': '分析失败',
                '主语群体类型': '分析失败',
                '宾语群体类型': '分析失败',
                '涉及强势群体': '分析失败',
                '涉及弱势群体': '分析失败',
                '群体关系': '分析失败',
                '完整分析': f'错误: {e}'
            }
            results.append(result)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame(results)
    
    # 将原始数据和分析结果合并
    final_df = pd.concat([df.iloc[:len(results)], result_df], axis=1)
    
    # 保存结果，使用时间戳避免文件冲突
    import datetime
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(desktop_path, f"事件_分析结果_{timestamp}.xlsx")
    final_df.to_excel(output_path, index=False)
    
    print(f"\n分析完成！结果已保存到: {output_path}")
    print(f"共分析了 {len(results)} 条新闻标题")
    
    # 显示前几条结果
    print("\n前5条分析结果预览:")
    for i, result in enumerate(results[:5]):
        print(f"\n{i+1}. {result['原标题']}")
        print(f"   主语: {result['主语']} ({result['主语群体类型']})")
        print(f"   谓语: {result['谓语']}")
        print(f"   宾语: {result['宾语']} ({result['宾语群体类型']})")
        print(f"   群体关系: {result['群体关系']}")
        print(f"   涉及强势群体: {result['涉及强势群体']}")
        print(f"   涉及弱势群体: {result['涉及弱势群体']}")

def analyze_groups(words, subject, object_word):
    """
    分析文本中的强势群体和弱势群体
    """
    # 将所有词语转换为字符串进行分析
    all_text = ' '.join(words) + ' ' + subject + ' ' + object_word

    # 查找强势群体和弱势群体
    strong_found = []
    weak_found = []

    for group in strong_groups:
        if group in all_text:
            strong_found.append(group)

    for group in weak_groups:
        if group in all_text:
            weak_found.append(group)

    # 分析主语和宾语的群体类型
    subject_type = "未知"
    object_type = "未知"

    for group in strong_groups:
        if group in subject:
            subject_type = "强势群体"
            break
    if subject_type == "未知":
        for group in weak_groups:
            if group in subject:
                subject_type = "弱势群体"
                break

    for group in strong_groups:
        if group in object_word:
            object_type = "强势群体"
            break
    if object_type == "未知":
        for group in weak_groups:
            if group in object_word:
                object_type = "弱势群体"
                break

    # 分析群体关系
    relationship = "未明确"
    if subject_type == "强势群体" and object_type == "弱势群体":
        relationship = "强势群体对弱势群体"
    elif subject_type == "弱势群体" and object_type == "强势群体":
        relationship = "弱势群体对强势群体"
    elif subject_type == "强势群体" and object_type == "强势群体":
        relationship = "强势群体之间"
    elif subject_type == "弱势群体" and object_type == "弱势群体":
        relationship = "弱势群体之间"
    elif subject_type == "强势群体" or object_type == "强势群体":
        relationship = "涉及强势群体"
    elif subject_type == "弱势群体" or object_type == "弱势群体":
        relationship = "涉及弱势群体"

    return {
        'subject_type': subject_type,
        'object_type': object_type,
        'strong_groups_found': ', '.join(strong_found) if strong_found else "无",
        'weak_groups_found': ', '.join(weak_found) if weak_found else "无",
        'relationship': relationship
    }

def analyze_groups(words, subject, object_word):
    """
    分析文本中的强势群体和弱势群体
    """
    # 将所有词语转换为字符串进行分析
    all_text = ' '.join(words) + ' ' + subject + ' ' + object_word

    # 查找强势群体和弱势群体
    strong_found = []
    weak_found = []

    for group in strong_groups:
        if group in all_text:
            strong_found.append(group)

    for group in weak_groups:
        if group in all_text:
            weak_found.append(group)

    # 分析主语和宾语的群体类型
    subject_type = "未知"
    object_type = "未知"

    # 检查主语是否包含群体标识
    for group in strong_groups:
        if group in subject:
            subject_type = "强势群体"
            break
    if subject_type == "未知":
        for group in weak_groups:
            if group in subject:
                subject_type = "弱势群体"
                break

    # 检查宾语是否包含群体标识
    for group in strong_groups:
        if group in object_word:
            object_type = "强势群体"
            break
    if object_type == "未知":
        for group in weak_groups:
            if group in object_word:
                object_type = "弱势群体"
                break

    # 分析群体关系
    relationship = "未明确"
    if subject_type == "强势群体" and object_type == "弱势群体":
        relationship = "强势群体对弱势群体"
    elif subject_type == "弱势群体" and object_type == "强势群体":
        relationship = "弱势群体对强势群体"
    elif subject_type == "强势群体" and object_type == "强势群体":
        relationship = "强势群体之间"
    elif subject_type == "弱势群体" and object_type == "弱势群体":
        relationship = "弱势群体之间"
    elif subject_type == "强势群体" or object_type == "强势群体":
        relationship = "涉及强势群体"
    elif subject_type == "弱势群体" or object_type == "弱势群体":
        relationship = "涉及弱势群体"

    return {
        'subject_type': subject_type,
        'object_type': object_type,
        'strong_groups_found': ', '.join(strong_found) if strong_found else "无",
        'weak_groups_found': ', '.join(weak_found) if weak_found else "无",
        'relationship': relationship
    }

def extract_svo(words, pos_tags, dependencies):
    """
    从依存句法分析结果中提取主谓宾，重点识别人物
    """
    subject = ""
    predicate = ""
    object_word = ""

    # 寻找根节点（通常是主要谓语）
    root_idx = -1
    for i, (head, relation) in enumerate(dependencies):
        if head == 0:  # 根节点
            root_idx = i
            break

    # 寻找主要动词作为谓语
    predicate_idx = -1
    if root_idx != -1 and pos_tags[root_idx].startswith('v'):
        predicate = words[root_idx]
        predicate_idx = root_idx
    else:
        # 寻找最重要的动词
        for i, pos in enumerate(pos_tags):
            if pos.startswith('v'):
                predicate_idx = i
                predicate = words[i]
                break
        if predicate_idx == -1 and root_idx != -1:
            predicate = words[root_idx]
            predicate_idx = root_idx

    # 寻找主语和宾语，优先识别人物
    subjects = []
    objects = []

    # 通过依存关系寻找
    for i, (head, relation) in enumerate(dependencies):
        if head > 0:
            head_idx = head - 1

            # 主语关系
            if relation in ['SBV', 'nsubj', 'nsubjpass']:
                if head_idx == predicate_idx or (predicate_idx == -1 and head_idx == root_idx):
                    # 扩展主语，包含修饰词
                    expanded_subject = expand_entity(words, i, dependencies, pos_tags)
                    subjects.append(expanded_subject)

            # 宾语关系
            elif relation in ['VOB', 'dobj', 'obj', 'iobj']:
                if head_idx == predicate_idx or (predicate_idx == -1 and head_idx == root_idx):
                    # 扩展宾语，包含修饰词
                    expanded_object = expand_entity(words, i, dependencies, pos_tags)
                    objects.append(expanded_object)

    # 如果没找到，使用启发式方法寻找人物
    if not subjects:
        subjects = find_person_entities(words, pos_tags, predicate_idx, "before")

    if not objects:
        objects = find_person_entities(words, pos_tags, predicate_idx, "after")

    # 组合结果
    subject = "+".join(subjects) if subjects else "未识别"
    object_word = "+".join(objects) if objects else "未识别"

    return subject, predicate or "未识别", object_word

def expand_entity(words, entity_idx, dependencies, pos_tags):
    """
    扩展实体，包含其修饰词
    """
    entity_words = [words[entity_idx]]

    # 寻找修饰这个实体的词
    for i, (head, relation) in enumerate(dependencies):
        if head == entity_idx + 1:  # 依存于当前实体
            if relation in ['ATT', 'amod', 'nmod']:  # 定语关系
                entity_words.insert(0, words[i])

    return "".join(entity_words)

def find_person_entities(words, pos_tags, predicate_idx, direction):
    """
    寻找人物实体
    """
    entities = []

    if predicate_idx == -1:
        return entities

    if direction == "before":
        search_range = range(predicate_idx-1, -1, -1)
    else:
        search_range = range(predicate_idx+1, len(words))

    for i in search_range:
        word = words[i]
        pos = pos_tags[i]

        # 检查是否是人物相关词汇
        if (pos.startswith('n') or pos.startswith('r') or
            any(group in word for group in strong_groups | weak_groups) or
            word in ['人', '男子', '女子', '男孩', '女孩', '大爷', '大妈', '师傅']):

            # 构建完整的人物描述
            person_desc = []

            # 向前收集修饰词
            j = i - 1
            while j >= 0 and (pos_tags[j].startswith('a') or pos_tags[j].startswith('m') or
                             words[j] in ['一', '某', '该', '这', '那']):
                person_desc.insert(0, words[j])
                j -= 1

            person_desc.append(word)

            # 向后收集修饰词
            j = i + 1
            while j < len(words) and pos_tags[j].startswith('n'):
                person_desc.append(words[j])
                j += 1

            entities.append("".join(person_desc))
            break  # 只取第一个找到的

    return entities

def format_detailed_analysis(words, pos_tags, dependencies):
    """
    格式化详细的分析结果
    """
    analysis = []
    for i, (word, pos) in enumerate(zip(words, pos_tags)):
        head, relation = dependencies[i]
        if head == 0:
            dep_info = "ROOT"
        else:
            dep_info = f"{relation}→{words[head-1]}"
        analysis.append(f"{word}({pos},{dep_info})")
    
    return " | ".join(analysis)

if __name__ == "__main__":
    analyze_news_titles()
