import pandas as pd
from ltp import LTP
import os

def test_person_svo():
    """
    测试人物主谓宾提取
    """
    print("=== 测试人物主谓宾提取 ===")
    
    # 测试用例
    test_titles = [
        "广州一商贩持铁棍追打城管",
        "湖北孝感一城管脱衣服踹倒卖菜老人", 
        "合肥城管与摊贩起冲突互殴",
        "城管抢走摊贩钱盒子",
        "广州一小贩用锹柄将城管头部打伤"
    ]
    
    print("正在初始化LTP...")
    try:
        ltp = LTP()
        print("LTP初始化成功")
    except Exception as e:
        print(f"LTP初始化失败: {e}")
        return
    
    for i, title in enumerate(test_titles):
        print(f"\n{i+1}. 分析: {title}")
        
        try:
            # LTP分析
            result = ltp.pipeline([title], tasks=["cws", "pos", "dep"])
            words = result.cws[0]
            pos_tags = result.pos[0]
            dependencies = list(zip(result.dep[0]['head'], result.dep[0]['label']))
            
            print(f"   分词: {' / '.join(words)}")
            
            # 简单的人物主谓宾提取
            subject, predicate, object_word = extract_simple_person_svo(words, pos_tags)
            
            print(f"   主语(人物): {subject}")
            print(f"   谓语: {predicate}")
            print(f"   宾语(人物): {object_word}")
            
        except Exception as e:
            print(f"   分析出错: {e}")

def extract_simple_person_svo(words, pos_tags):
    """
    简单的人物主谓宾提取
    """
    # 人物关键词
    person_words = {
        '城管', '警察', '商贩', '小贩', '摊贩', '老人', '学生', '市民',
        '店主', '男子', '女子', '大爷', '大妈', '人', '师傅'
    }
    
    subject = "无"
    predicate = "未识别"
    object_word = "无"
    
    # 寻找动词
    verb_idx = -1
    for i, pos in enumerate(pos_tags):
        if pos.startswith('v'):
            predicate = words[i]
            verb_idx = i
            break
    
    # 在动词前寻找人物主语
    if verb_idx != -1:
        for i in range(verb_idx-1, -1, -1):
            word = words[i]
            if any(person in word for person in person_words) or word in person_words:
                # 包含前面的修饰词
                subject_parts = []
                for j in range(max(0, i-2), i+1):
                    subject_parts.append(words[j])
                subject = "".join(subject_parts)
                break
    
    # 在动词后寻找人物宾语
    if verb_idx != -1:
        for i in range(verb_idx+1, len(words)):
            word = words[i]
            if any(person in word for person in person_words) or word in person_words:
                # 包含后面的修饰词
                object_parts = [word]
                for j in range(i+1, min(len(words), i+3)):
                    if pos_tags[j].startswith('n'):
                        object_parts.append(words[j])
                    else:
                        break
                object_word = "".join(object_parts)
                break
    
    return subject, predicate, object_word

if __name__ == "__main__":
    test_person_svo()
