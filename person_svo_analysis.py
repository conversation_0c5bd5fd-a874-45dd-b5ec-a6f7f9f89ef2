import pandas as pd
from ltp import LTP
import os
import datetime

# 定义强势群体和弱势群体
strong_groups = {"城管", "警察", "老师", "教官", "政府", "保安", "医生", "法官", "班主任"}
weak_groups = {"学生", "小贩", "摊贩", "市民", "孩子", "女孩", "老人", "男童", "女童", "百姓", "居民"}

def extract_person_svo(words, pos_tags, dependencies):
    """
    专门提取人物作为主语和宾语的主谓宾结构
    """
    subject = "无"
    predicate = "未识别"
    object_word = "无"
    
    # 扩展的人物词汇库
    person_keywords = {
        '城管', '警察', '老师', '教官', '政府', '保安', '医生', '法官', '班主任',
        '学生', '小贩', '摊贩', '市民', '孩子', '女孩', '老人', '男童', '女童', 
        '百姓', '居民', '商贩', '店主', '男子', '女子', '大爷', '大妈', '师傅',
        '人', '员工', '工人', '农民', '司机', '乘客', '顾客', '患者', '家长',
        '商户', '摊主', '贩子', '小商贩', '商人', '老板', '经理', '主任',
        '同学', '同事', '朋友', '邻居', '路人', '行人', '市民', '村民'
    }
    
    # 寻找根节点和主要动词
    root_idx = -1
    for i, (head, relation) in enumerate(dependencies):
        if head == 0:
            root_idx = i
            break
    
    # 寻找主要动词作为谓语
    predicate_idx = -1
    for i, pos in enumerate(pos_tags):
        if pos.startswith('v'):
            predicate = words[i]
            predicate_idx = i
            break
    
    if predicate_idx == -1 and root_idx != -1:
        predicate = words[root_idx]
        predicate_idx = root_idx
    
    # 通过依存关系寻找人物主语和宾语
    for i, (head, relation) in enumerate(dependencies):
        if head > 0:
            head_idx = head - 1
            word = words[i]
            
            # 主语关系
            if relation in ['SBV', 'nsubj', 'nsubjpass']:
                if head_idx == predicate_idx or (predicate_idx == -1 and head_idx == root_idx):
                    if is_person(word, person_keywords):
                        subject = expand_person_name(words, i)
            
            # 宾语关系
            elif relation in ['VOB', 'dobj', 'obj', 'iobj']:
                if head_idx == predicate_idx or (predicate_idx == -1 and head_idx == root_idx):
                    if is_person(word, person_keywords):
                        object_word = expand_person_name(words, i)
    
    # 如果依存关系没找到，使用位置和词性启发式搜索
    if subject == "无" and predicate_idx != -1:
        for i in range(predicate_idx-1, -1, -1):
            if is_person(words[i], person_keywords):
                subject = expand_person_name(words, i)
                break
    
    if object_word == "无" and predicate_idx != -1:
        for i in range(predicate_idx+1, len(words)):
            if is_person(words[i], person_keywords):
                object_word = expand_person_name(words, i)
                break
    
    return subject, predicate, object_word

def is_person(word, person_keywords):
    """
    判断词语是否指代人物
    """
    # 直接匹配
    if word in person_keywords:
        return True
    
    # 包含人物关键词
    if any(keyword in word for keyword in person_keywords):
        return True
    
    # 常见人物词汇模式
    person_patterns = [
        lambda w: w.endswith('人') and len(w) > 1,
        lambda w: w.endswith('者') and len(w) > 1,
        lambda w: w.endswith('员') and len(w) > 1,
        lambda w: w.endswith('师') and len(w) > 1,
        lambda w: w.endswith('长') and len(w) > 1,
        lambda w: w.endswith('手') and len(w) > 1,
        lambda w: w.endswith('工') and len(w) > 1,
        lambda w: w.endswith('民') and len(w) > 1,
        lambda w: w.endswith('客') and len(w) > 1,
        lambda w: w.startswith('老') and len(w) > 1,
        lambda w: w.startswith('小') and len(w) > 1 and not w in ['小区', '小车', '小店'],
        lambda w: w.startswith('大') and len(w) > 1 and w in ['大爷', '大妈', '大叔', '大婶']
    ]
    
    return any(pattern(word) for pattern in person_patterns)

def expand_person_name(words, person_idx):
    """
    扩展人物名称，包含修饰词
    """
    result = []
    
    # 向前收集修饰词
    i = person_idx - 1
    while i >= 0:
        word = words[i]
        if word in ['一', '某', '该', '这', '那', '两', '三', '多', '几'] or is_location_modifier(word):
            result.insert(0, word)
            i -= 1
        else:
            break
    
    # 添加核心人物词
    result.append(words[person_idx])
    
    # 向后收集修饰词（较少使用）
    i = person_idx + 1
    while i < len(words):
        word = words[i]
        if len(word) <= 2 and word not in ['的', '了', '着', '过', '被', '把', '将']:
            result.append(word)
            i += 1
        else:
            break
    
    return "".join(result)

def is_location_modifier(word):
    """
    判断是否是地点修饰词
    """
    locations = ['广州', '北京', '上海', '深圳', '杭州', '南京', '武汉', '成都', 
                '重庆', '天津', '西安', '沈阳', '大连', '青岛', '厦门', '苏州',
                '辽宁', '山东', '江苏', '浙江', '广东', '湖北', '湖南', '河南',
                '河北', '山西', '陕西', '四川', '云南', '贵州', '福建', '安徽']
    return word in locations or any(loc in word for loc in locations)

def analyze_groups(words, subject, object_word):
    """
    分析群体类型
    """
    all_text = ' '.join(words) + ' ' + subject + ' ' + object_word
    
    strong_found = [group for group in strong_groups if group in all_text]
    weak_found = [group for group in weak_groups if group in all_text]
    
    # 分析主语和宾语的群体类型
    subject_type = "未知"
    object_type = "未知"
    
    if subject != "无":
        for group in strong_groups:
            if group in subject:
                subject_type = "强势群体"
                break
        if subject_type == "未知":
            for group in weak_groups:
                if group in subject:
                    subject_type = "弱势群体"
                    break
            if subject_type == "未知":
                subject_type = "普通人物"
    
    if object_word != "无":
        for group in strong_groups:
            if group in object_word:
                object_type = "强势群体"
                break
        if object_type == "未知":
            for group in weak_groups:
                if group in object_word:
                    object_type = "弱势群体"
                    break
            if object_type == "未知":
                object_type = "普通人物"
    
    # 分析群体关系
    relationship = "未明确"
    if subject != "无" and object_word != "无":
        if subject_type == "强势群体" and object_type == "弱势群体":
            relationship = "强势群体对弱势群体"
        elif subject_type == "弱势群体" and object_type == "强势群体":
            relationship = "弱势群体对强势群体"
        elif subject_type == "强势群体" and object_type == "强势群体":
            relationship = "强势群体之间"
        elif subject_type == "弱势群体" and object_type == "弱势群体":
            relationship = "弱势群体之间"
        else:
            relationship = "人物之间"
    elif subject != "无":
        if subject_type == "强势群体":
            relationship = "涉及强势群体"
        elif subject_type == "弱势群体":
            relationship = "涉及弱势群体"
        else:
            relationship = "涉及人物"
    elif object_word != "无":
        if object_type == "强势群体":
            relationship = "涉及强势群体"
        elif object_type == "弱势群体":
            relationship = "涉及弱势群体"
        else:
            relationship = "涉及人物"
    
    return {
        'subject_type': subject_type,
        'object_type': object_type,
        'strong_groups_found': ', '.join(strong_found) if strong_found else "无",
        'weak_groups_found': ', '.join(weak_found) if weak_found else "无",
        'relationship': relationship
    }

def analyze_news_titles():
    """
    分析新闻标题 - 专注于人物主谓宾
    """
    print("=== 人物主谓宾分析系统 ===")
    print("正在初始化LTP模型...")
    ltp = LTP()
    
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    file_path = os.path.join(desktop_path, "事件.xlsx")
    
    try:
        df = pd.read_excel(file_path)
        print(f"成功读取文件，共 {len(df)} 条数据")
    except FileNotFoundError:
        print(f"未找到文件: {file_path}")
        return
    
    title_column = df.columns[0]
    titles = df[title_column].dropna().astype(str)
    
    results = []
    
    for idx, title in enumerate(titles):
        print(f"\n正在分析第 {idx+1}/{len(titles)} 条: {title}")
        
        try:
            # LTP分析
            result = ltp.pipeline([title], tasks=["cws", "pos", "dep"])
            words = result.cws[0]
            pos_tags = result.pos[0]
            dependencies = list(zip(result.dep[0]['head'], result.dep[0]['label']))
            
            # 提取人物主谓宾
            subject, predicate, object_word = extract_person_svo(words, pos_tags, dependencies)
            
            # 群体分析
            group_analysis = analyze_groups(words, subject, object_word)
            
            print(f"   主语(人物): {subject}")
            print(f"   谓语: {predicate}")
            print(f"   宾语(人物): {object_word}")
            print(f"   关系: {group_analysis['relationship']}")
            
            result_data = {
                '原标题': title,
                '分词结果': ' / '.join(words),
                '主语(人物)': subject,
                '谓语': predicate,
                '宾语(人物)': object_word,
                '主语群体类型': group_analysis['subject_type'],
                '宾语群体类型': group_analysis['object_type'],
                '涉及强势群体': group_analysis['strong_groups_found'],
                '涉及弱势群体': group_analysis['weak_groups_found'],
                '群体关系': group_analysis['relationship'],
                '完整分析': ' | '.join([f"{w}({p})" for w, p in zip(words, pos_tags)])
            }
            results.append(result_data)
            
        except Exception as e:
            print(f"分析出错: {e}")
            results.append({
                '原标题': title,
                '分词结果': '分析失败',
                '主语(人物)': '分析失败',
                '谓语': '分析失败',
                '宾语(人物)': '分析失败',
                '主语群体类型': '分析失败',
                '宾语群体类型': '分析失败',
                '涉及强势群体': '分析失败',
                '涉及弱势群体': '分析失败',
                '群体关系': '分析失败',
                '完整分析': f'错误: {e}'
            })
    
    # 保存结果
    result_df = pd.DataFrame(results)
    final_df = pd.concat([df.iloc[:len(results)], result_df], axis=1)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(desktop_path, f"事件_人物主谓宾分析_{timestamp}.xlsx")
    final_df.to_excel(output_path, index=False)
    
    print(f"\n分析完成！结果已保存到: {output_path}")
    
    # 统计分析
    print("\n=== 统计分析 ===")
    
    # 有人物主语的数量
    has_subject = sum(1 for r in results if r['主语(人物)'] != '无')
    has_object = sum(1 for r in results if r['宾语(人物)'] != '无')
    has_both = sum(1 for r in results if r['主语(人物)'] != '无' and r['宾语(人物)'] != '无')
    
    print(f"有人物主语的标题: {has_subject}/{len(results)} ({has_subject/len(results)*100:.1f}%)")
    print(f"有人物宾语的标题: {has_object}/{len(results)} ({has_object/len(results)*100:.1f}%)")
    print(f"主语宾语都是人物的标题: {has_both}/{len(results)} ({has_both/len(results)*100:.1f}%)")
    
    # 群体关系统计
    relationship_counts = {}
    for r in results:
        rel = r['群体关系']
        relationship_counts[rel] = relationship_counts.get(rel, 0) + 1
    
    print("\n群体关系分布:")
    for relation, count in relationship_counts.items():
        print(f"  {relation}: {count}条")

if __name__ == "__main__":
    analyze_news_titles()
