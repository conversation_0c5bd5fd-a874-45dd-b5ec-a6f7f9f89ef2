{"cells": [{"cell_type": "code", "execution_count": 3, "id": "585acc7b-01d3-4503-b61b-f95179bd12b6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting ltp\n", "  Downloading ltp-4.2.14-py3-none-any.whl.metadata (13 kB)\n", "Collecting ltp-core>=0.1.3 (from ltp)\n", "  Downloading ltp_core-0.1.4-py3-none-any.whl.metadata (2.7 kB)\n", "Collecting ltp-extension>=0.1.9 (from ltp)\n", "  Downloading ltp_extension-0.1.13-cp37-abi3-win_amd64.whl.metadata (7.0 kB)\n", "Collecting huggingface-hub>=0.8.0 (from ltp)\n", "  Downloading huggingface_hub-0.33.2-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: filelock in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (3.13.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (2024.3.1)\n", "Requirement already satisfied: packaging>=20.9 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (23.2)\n", "Requirement already satisfied: pyyaml>=5.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (6.0.1)\n", "Requirement already satisfied: requests in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (2.32.2)\n", "Requirement already satisfied: tqdm>=4.42.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (4.66.4)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (4.13.2)\n", "Collecting torch>=1.6.0 (from ltp-core>=0.1.3->ltp)\n", "  Downloading torch-2.7.1-cp312-cp312-win_amd64.whl.metadata (28 kB)\n", "Collecting transformers>=4.0.0 (from ltp-core>=0.1.3->ltp)\n", "  Downloading transformers-4.53.1-py3-none-any.whl.metadata (40 kB)\n", "     ---------------------------------------- 0.0/40.9 kB ? eta -:--:--\n", "     ---------- ----------------------------- 10.2/40.9 kB ? eta -:--:--\n", "     -------------------------------------- 40.9/40.9 kB 393.2 kB/s eta 0:00:00\n", "Collecting sympy>=1.13.3 (from torch>=1.6.0->ltp-core>=0.1.3->ltp)\n", "  Downloading sympy-1.14.0-py3-none-any.whl.metadata (12 kB)\n", "Requirement already satisfied: networkx in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (3.2.1)\n", "Requirement already satisfied: jinja2 in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (3.1.4)\n", "Requirement already satisfied: setuptools in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (69.5.1)\n", "Requirement already satisfied: colorama in d:\\anaconda\\anaconda\\lib\\site-packages (from tqdm>=4.42.1->huggingface-hub>=0.8.0->ltp) (0.4.6)\n", "Requirement already satisfied: numpy>=1.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (1.26.4)\n", "Requirement already satisfied: regex!=2019.12.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (2023.10.3)\n", "Collecting tokenizers<0.22,>=0.21 (from transformers>=4.0.0->ltp-core>=0.1.3->ltp)\n", "  Downloading tokenizers-0.21.2-cp39-abi3-win_amd64.whl.metadata (6.9 kB)\n", "Collecting safetensors>=0.4.3 (from transformers>=4.0.0->ltp-core>=0.1.3->ltp)\n", "  Downloading safetensors-0.5.3-cp38-abi3-win_amd64.whl.metadata (3.9 kB)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2025.6.15)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from sympy>=1.13.3->torch>=1.6.0->ltp-core>=0.1.3->ltp) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from jinja2->torch>=1.6.0->ltp-core>=0.1.3->ltp) (2.1.3)\n", "Downloading ltp-4.2.14-py3-none-any.whl (20 kB)\n", "Downloading huggingface_hub-0.33.2-py3-none-any.whl (515 kB)\n", "   ---------------------------------------- 0.0/515.4 kB ? eta -:--:--\n", "    --------------------------------------- 10.2/515.4 kB ? eta -:--:--\n", "   --- ----------------------------------- 41.0/515.4 kB 393.8 kB/s eta 0:00:02\n", "   ---- ---------------------------------- 61.4/515.4 kB 409.6 kB/s eta 0:00:02\n", "   ------ -------------------------------- 92.2/515.4 kB 476.3 kB/s eta 0:00:01\n", "   -------- ----------------------------- 112.6/515.4 kB 504.4 kB/s eta 0:00:01\n", "   --------- ---------------------------- 122.9/515.4 kB 423.5 kB/s eta 0:00:01\n", "   ------------ ------------------------- 174.1/515.4 kB 499.5 kB/s eta 0:00:01\n", "   -------------- ----------------------- 194.6/515.4 kB 562.0 kB/s eta 0:00:01\n", "   ----------------- -------------------- 235.5/515.4 kB 576.2 kB/s eta 0:00:01\n", "   ----------------- -------------------- 235.5/515.4 kB 576.2 kB/s eta 0:00:01\n", "   --------------------------------- ---- 450.6/515.4 kB 880.6 kB/s eta 0:00:01\n", "   -------------------------------------- 515.4/515.4 kB 923.5 kB/s eta 0:00:00\n", "Downloading ltp_core-0.1.4-py3-none-any.whl (66 kB)\n", "   ---------------------------------------- 0.0/66.5 kB ? eta -:--:--\n", "   ---------------------------------------- 66.5/66.5 kB 3.5 MB/s eta 0:00:00\n", "Downloading ltp_extension-0.1.13-cp37-abi3-win_amd64.whl (707 kB)\n", "   ---------------------------------------- 0.0/707.5 kB ? eta -:--:--\n", "   ------------- -------------------------- 245.8/707.5 kB 5.0 MB/s eta 0:00:01\n", "   ----------------------- ---------------- 409.6/707.5 kB 4.2 MB/s eta 0:00:01\n", "   --------------------------------- ------ 593.9/707.5 kB 4.7 MB/s eta 0:00:01\n", "   ---------------------------------------- 707.5/707.5 kB 3.7 MB/s eta 0:00:00\n", "Downloading torch-2.7.1-cp312-cp312-win_amd64.whl (216.1 MB)\n", "   ---------------------------------------- 0.0/216.1 MB ? eta -:--:--\n", "   ---------------------------------------- 0.3/216.1 MB 7.0 MB/s eta 0:00:31\n", "   ---------------------------------------- 0.7/216.1 MB 7.2 MB/s eta 0:00:30\n", "   ---------------------------------------- 1.0/216.1 MB 8.8 MB/s eta 0:00:25\n", "   ---------------------------------------- 1.0/216.1 MB 5.3 MB/s eta 0:00:41\n", "   ---------------------------------------- 1.5/216.1 MB 6.6 MB/s eta 0:00:33\n", "   ---------------------------------------- 2.3/216.1 MB 8.2 MB/s eta 0:00:27\n", "   ---------------------------------------- 2.5/216.1 MB 8.4 MB/s eta 0:00:26\n", "    --------------------------------------- 3.2/216.1 MB 8.5 MB/s eta 0:00:26\n", "    --------------------------------------- 3.8/216.1 MB 9.1 MB/s eta 0:00:24\n", "    --------------------------------------- 3.9/216.1 MB 9.1 MB/s eta 0:00:24\n", "    --------------------------------------- 4.0/216.1 MB 8.2 MB/s eta 0:00:26\n", "    --------------------------------------- 4.6/216.1 MB 8.5 MB/s eta 0:00:25\n", "    --------------------------------------- 4.6/216.1 MB 8.5 MB/s eta 0:00:25\n", "    --------------------------------------- 4.8/216.1 MB 7.4 MB/s eta 0:00:29\n", "    --------------------------------------- 5.2/216.1 MB 7.8 MB/s eta 0:00:28\n", "   - -------------------------------------- 5.7/216.1 MB 7.8 MB/s eta 0:00:28\n", "   - -------------------------------------- 6.1/216.1 MB 7.7 MB/s eta 0:00:28\n", "   - -------------------------------------- 6.3/216.1 MB 7.6 MB/s eta 0:00:28\n", "   - -------------------------------------- 6.6/216.1 MB 7.5 MB/s eta 0:00:28\n", "   - -------------------------------------- 6.9/216.1 MB 7.4 MB/s eta 0:00:29\n", "   - -------------------------------------- 7.1/216.1 MB 7.3 MB/s eta 0:00:29\n", "   - -------------------------------------- 7.4/216.1 MB 7.2 MB/s eta 0:00:29\n", "   - -------------------------------------- 7.6/216.1 MB 7.2 MB/s eta 0:00:30\n", "   - -------------------------------------- 7.9/216.1 MB 7.1 MB/s eta 0:00:30\n", "   - -------------------------------------- 8.1/216.1 MB 7.0 MB/s eta 0:00:30\n", "   - -------------------------------------- 8.4/216.1 MB 7.0 MB/s eta 0:00:30\n", "   - -------------------------------------- 8.7/216.1 MB 6.9 MB/s eta 0:00:30\n", "   - -------------------------------------- 8.9/216.1 MB 6.9 MB/s eta 0:00:31\n", "   - -------------------------------------- 9.2/216.1 MB 6.8 MB/s eta 0:00:31\n", "   - -------------------------------------- 9.4/216.1 MB 6.8 MB/s eta 0:00:31\n", "   - -------------------------------------- 9.7/216.1 MB 6.7 MB/s eta 0:00:31\n", "   - -------------------------------------- 10.0/216.1 MB 6.8 MB/s eta 0:00:31\n", "   - -------------------------------------- 10.2/216.1 MB 6.7 MB/s eta 0:00:31\n", "   - -------------------------------------- 10.5/216.1 MB 6.7 MB/s eta 0:00:31\n", "   - -------------------------------------- 10.7/216.1 MB 6.7 MB/s eta 0:00:31\n", "   -- ------------------------------------- 11.0/216.1 MB 6.5 MB/s eta 0:00:32\n", "   -- ------------------------------------- 11.2/216.1 MB 6.7 MB/s eta 0:00:31\n", "   -- ------------------------------------- 11.5/216.1 MB 6.6 MB/s eta 0:00:31\n", "   -- ------------------------------------- 11.8/216.1 MB 6.5 MB/s eta 0:00:32\n", "   -- ------------------------------------- 12.1/216.1 MB 6.5 MB/s eta 0:00:32\n", "   -- ------------------------------------- 12.4/216.1 MB 6.3 MB/s eta 0:00:33\n", "   -- ------------------------------------- 12.7/216.1 MB 6.2 MB/s eta 0:00:33\n", "   -- ------------------------------------- 13.0/216.1 MB 6.2 MB/s eta 0:00:33\n", "   -- ------------------------------------- 13.4/216.1 MB 6.2 MB/s eta 0:00:33\n", "   -- ------------------------------------- 13.8/216.1 MB 6.1 MB/s eta 0:00:34\n", "   -- ------------------------------------- 14.3/216.1 MB 6.4 MB/s eta 0:00:32\n", "   -- ------------------------------------- 14.5/216.1 MB 6.4 MB/s eta 0:00:32\n", "   -- ------------------------------------- 14.8/216.1 MB 6.2 MB/s eta 0:00:33\n", "   -- ------------------------------------- 15.0/216.1 MB 6.4 MB/s eta 0:00:32\n", "   -- ------------------------------------- 15.1/216.1 MB 6.4 MB/s eta 0:00:32\n", "   -- ------------------------------------- 15.7/216.1 MB 6.2 MB/s eta 0:00:33\n", "   -- ------------------------------------- 16.0/216.1 MB 6.2 MB/s eta 0:00:33\n", "   --- ------------------------------------ 16.3/216.1 MB 6.2 MB/s eta 0:00:33\n", "   --- ------------------------------------ 16.6/216.1 MB 6.2 MB/s eta 0:00:32\n", "   --- ------------------------------------ 16.9/216.1 MB 6.2 MB/s eta 0:00:32\n", "   --- ------------------------------------ 17.1/216.1 MB 6.2 MB/s eta 0:00:32\n", "   --- ------------------------------------ 17.4/216.1 MB 6.2 MB/s eta 0:00:32\n", "   --- ------------------------------------ 17.7/216.1 MB 6.2 MB/s eta 0:00:32\n", "   --- ------------------------------------ 18.0/216.1 MB 6.3 MB/s eta 0:00:32\n", "   --- ------------------------------------ 18.2/216.1 MB 6.2 MB/s eta 0:00:32\n", "   --- ------------------------------------ 18.5/216.1 MB 6.3 MB/s eta 0:00:32\n", "   --- ------------------------------------ 18.8/216.1 MB 6.3 MB/s eta 0:00:32\n", "   --- ------------------------------------ 19.1/216.1 MB 6.4 MB/s eta 0:00:31\n", "   --- ------------------------------------ 19.3/216.1 MB 6.3 MB/s eta 0:00:32\n", "   --- ------------------------------------ 19.6/216.1 MB 6.3 MB/s eta 0:00:32\n", "   --- ------------------------------------ 19.9/216.1 MB 6.3 MB/s eta 0:00:32\n", "   --- ------------------------------------ 20.2/216.1 MB 6.4 MB/s eta 0:00:31\n", "   --- ------------------------------------ 20.5/216.1 MB 6.4 MB/s eta 0:00:31\n", "   --- ------------------------------------ 20.7/216.1 MB 6.4 MB/s eta 0:00:31\n", "   --- ------------------------------------ 21.0/216.1 MB 6.4 MB/s eta 0:00:31\n", "   --- ------------------------------------ 21.3/216.1 MB 6.4 MB/s eta 0:00:31\n", "   --- ------------------------------------ 21.6/216.1 MB 6.4 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 21.9/216.1 MB 6.5 MB/s eta 0:00:30\n", "   ---- ----------------------------------- 22.1/216.1 MB 6.5 MB/s eta 0:00:30\n", "   ---- ----------------------------------- 22.3/216.1 MB 6.5 MB/s eta 0:00:30\n", "   ---- ----------------------------------- 22.7/216.1 MB 6.4 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 23.0/216.1 MB 6.4 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 23.3/216.1 MB 6.4 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 23.7/216.1 MB 6.5 MB/s eta 0:00:30\n", "   ---- ----------------------------------- 23.9/216.1 MB 6.4 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 24.3/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 24.4/216.1 MB 6.2 MB/s eta 0:00:32\n", "   ---- ----------------------------------- 24.8/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 24.9/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 25.4/216.1 MB 6.4 MB/s eta 0:00:30\n", "   ---- ----------------------------------- 25.7/216.1 MB 6.3 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 26.0/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 26.3/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 26.6/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ---- ----------------------------------- 26.8/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 27.1/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 27.4/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 27.7/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 27.9/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 28.2/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 28.5/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 28.8/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 29.1/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 29.3/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 29.6/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 29.9/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 30.1/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 30.4/216.1 MB 6.2 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 30.7/216.1 MB 6.1 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 30.9/216.1 MB 6.1 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 31.2/216.1 MB 6.1 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 31.5/216.1 MB 6.1 MB/s eta 0:00:31\n", "   ----- ---------------------------------- 31.8/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ----- ---------------------------------- 32.2/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 32.5/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 32.8/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 33.0/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 33.0/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 33.6/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 33.7/216.1 MB 6.1 MB/s eta 0:00:30\n", "   ------ --------------------------------- 34.2/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 34.6/216.1 MB 6.3 MB/s eta 0:00:29\n", "   ------ --------------------------------- 34.8/216.1 MB 6.3 MB/s eta 0:00:29\n", "   ------ --------------------------------- 35.1/216.1 MB 6.4 MB/s eta 0:00:29\n", "   ------ --------------------------------- 35.4/216.1 MB 6.3 MB/s eta 0:00:29\n", "   ------ --------------------------------- 35.7/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 36.0/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 36.3/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 36.6/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 36.8/216.1 MB 6.2 MB/s eta 0:00:30\n", "   ------ --------------------------------- 37.1/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------ --------------------------------- 37.4/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------ --------------------------------- 37.7/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------- -------------------------------- 38.0/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------- -------------------------------- 38.3/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------- -------------------------------- 38.6/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------- -------------------------------- 38.8/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------- -------------------------------- 39.1/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------- -------------------------------- 39.4/216.1 MB 6.3 MB/s eta 0:00:29\n", "   ------- -------------------------------- 39.6/216.1 MB 6.2 MB/s eta 0:00:29\n", "   ------- -------------------------------- 40.0/216.1 MB 6.3 MB/s eta 0:00:28\n", "   ------- -------------------------------- 40.2/216.1 MB 6.3 MB/s eta 0:00:28\n", "   ------- -------------------------------- 40.5/216.1 MB 6.3 MB/s eta 0:00:28\n", "   ------- -------------------------------- 40.8/216.1 MB 6.3 MB/s eta 0:00:28\n", "   ------- -------------------------------- 41.0/216.1 MB 6.3 MB/s eta 0:00:28\n", "   ------- -------------------------------- 41.4/216.1 MB 6.3 MB/s eta 0:00:28\n", "   ------- -------------------------------- 41.5/216.1 MB 6.2 MB/s eta 0:00:28\n", "   ------- -------------------------------- 41.9/216.1 MB 6.2 MB/s eta 0:00:28\n", "   ------- -------------------------------- 42.2/216.1 MB 6.2 MB/s eta 0:00:28\n", "   ------- -------------------------------- 42.5/216.1 MB 6.2 MB/s eta 0:00:28\n", "   ------- -------------------------------- 42.8/216.1 MB 6.2 MB/s eta 0:00:28\n", "   ------- -------------------------------- 43.0/216.1 MB 6.1 MB/s eta 0:00:29\n", "   -------- ------------------------------- 43.4/216.1 MB 6.4 MB/s eta 0:00:27\n", "   -------- ------------------------------- 43.5/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 43.8/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 44.2/216.1 MB 6.3 MB/s eta 0:00:28\n", "   -------- ------------------------------- 44.4/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 44.7/216.1 MB 6.1 MB/s eta 0:00:29\n", "   -------- ------------------------------- 45.0/216.1 MB 6.1 MB/s eta 0:00:28\n", "   -------- ------------------------------- 45.3/216.1 MB 6.1 MB/s eta 0:00:28\n", "   -------- ------------------------------- 45.6/216.1 MB 6.1 MB/s eta 0:00:28\n", "   -------- ------------------------------- 45.8/216.1 MB 6.1 MB/s eta 0:00:28\n", "   -------- ------------------------------- 46.2/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 46.5/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 46.7/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 46.9/216.1 MB 6.1 MB/s eta 0:00:28\n", "   -------- ------------------------------- 47.4/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 47.5/216.1 MB 6.1 MB/s eta 0:00:28\n", "   -------- ------------------------------- 47.8/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 48.2/216.1 MB 6.2 MB/s eta 0:00:28\n", "   -------- ------------------------------- 48.4/216.1 MB 6.1 MB/s eta 0:00:28\n", "   --------- ------------------------------ 48.7/216.1 MB 6.1 MB/s eta 0:00:28\n", "   --------- ------------------------------ 49.0/216.1 MB 6.2 MB/s eta 0:00:28\n", "   --------- ------------------------------ 49.3/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 49.5/216.1 MB 6.1 MB/s eta 0:00:28\n", "   --------- ------------------------------ 49.9/216.1 MB 6.3 MB/s eta 0:00:27\n", "   --------- ------------------------------ 50.2/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 50.3/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 50.6/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 50.9/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 51.2/216.1 MB 6.1 MB/s eta 0:00:27\n", "   --------- ------------------------------ 51.5/216.1 MB 6.1 MB/s eta 0:00:27\n", "   --------- ------------------------------ 51.8/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 52.2/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 52.4/216.1 MB 6.1 MB/s eta 0:00:27\n", "   --------- ------------------------------ 52.7/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 53.0/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 53.3/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 53.3/216.1 MB 6.2 MB/s eta 0:00:27\n", "   --------- ------------------------------ 53.4/216.1 MB 5.9 MB/s eta 0:00:28\n", "   --------- ------------------------------ 53.9/216.1 MB 6.0 MB/s eta 0:00:28\n", "   ---------- ----------------------------- 54.3/216.1 MB 6.0 MB/s eta 0:00:27\n", "   ---------- ----------------------------- 54.7/216.1 MB 6.1 MB/s eta 0:00:27\n", "   ---------- ----------------------------- 55.1/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 55.2/216.1 MB 6.2 MB/s eta 0:00:27\n", "   ---------- ----------------------------- 55.6/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 55.9/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 55.9/216.1 MB 6.1 MB/s eta 0:00:27\n", "   ---------- ----------------------------- 56.3/216.1 MB 6.1 MB/s eta 0:00:27\n", "   ---------- ----------------------------- 56.6/216.1 MB 6.1 MB/s eta 0:00:27\n", "   ---------- ----------------------------- 57.0/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 57.3/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 57.6/216.1 MB 6.1 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 58.0/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 58.3/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 58.4/216.1 MB 6.1 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 58.7/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 59.1/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ---------- ----------------------------- 59.3/216.1 MB 6.1 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 59.6/216.1 MB 6.3 MB/s eta 0:00:25\n", "   ----------- ---------------------------- 59.8/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 60.1/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 60.5/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 60.6/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 61.1/216.1 MB 6.2 MB/s eta 0:00:25\n", "   ----------- ---------------------------- 61.3/216.1 MB 6.3 MB/s eta 0:00:25\n", "   ----------- ---------------------------- 61.5/216.1 MB 6.2 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 61.8/216.1 MB 6.2 MB/s eta 0:00:25\n", "   ----------- ---------------------------- 62.1/216.1 MB 6.1 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 62.4/216.1 MB 6.1 MB/s eta 0:00:26\n", "   ----------- ---------------------------- 62.6/216.1 MB 6.2 MB/s eta 0:00:25\n", "   ----------- ---------------------------- 63.1/216.1 MB 6.2 MB/s eta 0:00:25\n", "   ----------- ---------------------------- 63.3/216.1 MB 6.2 MB/s eta 0:00:25\n", "   ----------- ---------------------------- 63.6/216.1 MB 6.6 MB/s eta 0:00:24\n", "   ----------- ---------------------------- 63.9/216.1 MB 6.5 MB/s eta 0:00:24\n", "   ----------- ---------------------------- 64.2/216.1 MB 6.4 MB/s eta 0:00:24\n", "   ----------- ---------------------------- 64.5/216.1 MB 6.4 MB/s eta 0:00:24\n", "   ----------- ---------------------------- 64.8/216.1 MB 6.3 MB/s eta 0:00:25\n", "   ------------ --------------------------- 65.0/216.1 MB 6.3 MB/s eta 0:00:24\n", "   ------------ --------------------------- 65.3/216.1 MB 6.2 MB/s eta 0:00:25\n", "   ------------ --------------------------- 65.5/216.1 MB 6.3 MB/s eta 0:00:24\n", "   ------------ --------------------------- 65.8/216.1 MB 6.4 MB/s eta 0:00:24\n", "   ------------ --------------------------- 66.2/216.1 MB 6.3 MB/s eta 0:00:24\n", "   ------------ --------------------------- 66.5/216.1 MB 6.3 MB/s eta 0:00:24\n", "   ------------ --------------------------- 66.7/216.1 MB 6.3 MB/s eta 0:00:24\n", "   ------------ --------------------------- 67.0/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------ --------------------------- 67.3/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------ --------------------------- 67.6/216.1 MB 6.2 MB/s eta 0:00:25\n", "   ------------ --------------------------- 67.9/216.1 MB 6.1 MB/s eta 0:00:25\n", "   ------------ --------------------------- 68.2/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------ --------------------------- 68.4/216.1 MB 6.1 MB/s eta 0:00:25\n", "   ------------ --------------------------- 68.7/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------ --------------------------- 69.0/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------ --------------------------- 69.3/216.1 MB 6.3 MB/s eta 0:00:24\n", "   ------------ --------------------------- 69.6/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------ --------------------------- 69.8/216.1 MB 6.1 MB/s eta 0:00:24\n", "   ------------ --------------------------- 70.1/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 70.4/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 70.7/216.1 MB 6.4 MB/s eta 0:00:23\n", "   ------------- -------------------------- 71.0/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 71.3/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 71.6/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 71.9/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 72.2/216.1 MB 6.4 MB/s eta 0:00:23\n", "   ------------- -------------------------- 72.4/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 72.7/216.1 MB 6.2 MB/s eta 0:00:23\n", "   ------------- -------------------------- 73.0/216.1 MB 6.2 MB/s eta 0:00:23\n", "   ------------- -------------------------- 73.3/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 73.6/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 73.8/216.1 MB 6.2 MB/s eta 0:00:24\n", "   ------------- -------------------------- 74.1/216.1 MB 6.1 MB/s eta 0:00:24\n", "   ------------- -------------------------- 74.4/216.1 MB 6.1 MB/s eta 0:00:24\n", "   ------------- -------------------------- 74.7/216.1 MB 6.1 MB/s eta 0:00:24\n", "   ------------- -------------------------- 75.0/216.1 MB 6.1 MB/s eta 0:00:24\n", "   ------------- -------------------------- 75.2/216.1 MB 6.1 MB/s eta 0:00:24\n", "   ------------- -------------------------- 75.5/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 75.8/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 76.1/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 76.4/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 76.7/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 77.0/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 77.2/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 77.5/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 77.8/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 78.0/216.1 MB 6.1 MB/s eta 0:00:23\n", "   -------------- ------------------------- 78.3/216.1 MB 6.1 MB/s eta 0:00:23\n", "   -------------- ------------------------- 78.4/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 78.9/216.1 MB 6.1 MB/s eta 0:00:23\n", "   -------------- ------------------------- 79.2/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 79.5/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 79.6/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 79.9/216.1 MB 6.2 MB/s eta 0:00:23\n", "   -------------- ------------------------- 80.1/216.1 MB 6.1 MB/s eta 0:00:23\n", "   -------------- ------------------------- 80.4/216.1 MB 6.1 MB/s eta 0:00:23\n", "   -------------- ------------------------- 80.7/216.1 MB 6.1 MB/s eta 0:00:23\n", "   --------------- ------------------------ 81.1/216.1 MB 6.1 MB/s eta 0:00:23\n", "   --------------- ------------------------ 81.4/216.1 MB 6.1 MB/s eta 0:00:23\n", "   --------------- ------------------------ 81.8/216.1 MB 6.2 MB/s eta 0:00:22\n", "   --------------- ------------------------ 81.9/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 82.2/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 82.3/216.1 MB 6.0 MB/s eta 0:00:23\n", "   --------------- ------------------------ 82.7/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 82.9/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 83.3/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 83.6/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 84.0/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 84.2/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 84.7/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 85.0/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 85.1/216.1 MB 6.2 MB/s eta 0:00:22\n", "   --------------- ------------------------ 85.5/216.1 MB 6.2 MB/s eta 0:00:22\n", "   --------------- ------------------------ 85.8/216.1 MB 6.2 MB/s eta 0:00:22\n", "   --------------- ------------------------ 86.1/216.1 MB 6.1 MB/s eta 0:00:22\n", "   --------------- ------------------------ 86.4/216.1 MB 6.1 MB/s eta 0:00:22\n", "   ---------------- ----------------------- 86.7/216.1 MB 6.1 MB/s eta 0:00:22\n", "   ---------------- ----------------------- 87.0/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 87.2/216.1 MB 6.1 MB/s eta 0:00:22\n", "   ---------------- ----------------------- 87.5/216.1 MB 6.1 MB/s eta 0:00:22\n", "   ---------------- ----------------------- 87.8/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 88.1/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 88.4/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 88.6/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 88.9/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 89.2/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 89.5/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 89.8/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 90.0/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 90.4/216.1 MB 6.4 MB/s eta 0:00:20\n", "   ---------------- ----------------------- 90.7/216.1 MB 6.3 MB/s eta 0:00:20\n", "   ---------------- ----------------------- 90.9/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 91.3/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ---------------- ----------------------- 91.6/216.1 MB 6.3 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 91.9/216.1 MB 6.2 MB/s eta 0:00:21\n", "   ----------------- ---------------------- 92.1/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 92.5/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 92.6/216.1 MB 6.5 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 93.0/216.1 MB 6.4 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 93.2/216.1 MB 6.4 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 93.5/216.1 MB 6.3 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 93.8/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 94.0/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 94.2/216.1 MB 6.1 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 94.7/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 94.9/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 95.2/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 95.6/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 95.7/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 96.0/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 96.4/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 96.6/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 96.9/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ----------------- ---------------------- 97.2/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ------------------ --------------------- 97.4/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ------------------ --------------------- 97.8/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 98.0/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ------------------ --------------------- 98.3/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ------------------ --------------------- 98.6/216.1 MB 6.2 MB/s eta 0:00:20\n", "   ------------------ --------------------- 98.9/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 99.1/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 99.4/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 99.8/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 100.0/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 100.3/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 100.6/216.1 MB 6.1 MB/s eta 0:00:19\n", "   ------------------ --------------------- 100.9/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 101.2/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 101.6/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 101.7/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------ --------------------- 102.0/216.1 MB 6.1 MB/s eta 0:00:19\n", "   ------------------ --------------------- 102.3/216.1 MB 6.1 MB/s eta 0:00:19\n", "   ------------------ --------------------- 102.6/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------- -------------------- 102.9/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------- -------------------- 103.2/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------- -------------------- 103.5/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------- -------------------- 103.8/216.1 MB 6.2 MB/s eta 0:00:18\n", "   ------------------- -------------------- 104.0/216.1 MB 6.1 MB/s eta 0:00:19\n", "   ------------------- -------------------- 104.4/216.1 MB 6.2 MB/s eta 0:00:18\n", "   ------------------- -------------------- 104.8/216.1 MB 6.2 MB/s eta 0:00:19\n", "   ------------------- -------------------- 105.1/216.1 MB 6.2 MB/s eta 0:00:18\n", "   ------------------- -------------------- 105.3/216.1 MB 6.1 MB/s eta 0:00:19\n", "   ------------------- -------------------- 105.7/216.1 MB 6.2 MB/s eta 0:00:18\n", "   ------------------- -------------------- 105.9/216.1 MB 6.1 MB/s eta 0:00:19\n", "   ------------------- -------------------- 106.2/216.1 MB 6.1 MB/s eta 0:00:18\n", "   ------------------- -------------------- 106.5/216.1 MB 6.1 MB/s eta 0:00:18\n", "   ------------------- -------------------- 106.7/216.1 MB 6.2 MB/s eta 0:00:18\n", "   ------------------- -------------------- 107.1/216.1 MB 6.2 MB/s eta 0:00:18\n", "   ------------------- -------------------- 107.3/216.1 MB 6.2 MB/s eta 0:00:18\n", "   ------------------- -------------------- 107.7/216.1 MB 6.3 MB/s eta 0:00:18\n", "   ------------------- -------------------- 107.9/216.1 MB 6.1 MB/s eta 0:00:18\n", "   -------------------- ------------------- 108.2/216.1 MB 6.2 MB/s eta 0:00:18\n", "   -------------------- ------------------- 108.4/216.1 MB 6.2 MB/s eta 0:00:18\n", "   -------------------- ------------------- 108.6/216.1 MB 6.2 MB/s eta 0:00:18\n", "   -------------------- ------------------- 108.8/216.1 MB 6.1 MB/s eta 0:00:18\n", "   -------------------- ------------------- 109.1/216.1 MB 6.1 MB/s eta 0:00:18\n", "   -------------------- ------------------- 109.4/216.1 MB 6.1 MB/s eta 0:00:18\n", "   -------------------- ------------------- 109.9/216.1 MB 6.1 MB/s eta 0:00:18\n", "   -------------------- ------------------- 110.1/216.1 MB 6.1 MB/s eta 0:00:18\n", "   -------------------- ------------------- 110.5/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 110.8/216.1 MB 6.3 MB/s eta 0:00:17\n", "   -------------------- ------------------- 111.0/216.1 MB 6.2 MB/s eta 0:00:18\n", "   -------------------- ------------------- 111.4/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 111.6/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 111.9/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 112.2/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 112.4/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 112.8/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 113.0/216.1 MB 6.2 MB/s eta 0:00:17\n", "   -------------------- ------------------- 113.4/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 113.5/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 113.9/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 114.1/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 114.4/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 114.8/216.1 MB 6.3 MB/s eta 0:00:17\n", "   --------------------- ------------------ 115.0/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 115.4/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 115.4/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 115.7/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 116.0/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 116.4/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 116.8/216.1 MB 6.2 MB/s eta 0:00:16\n", "   --------------------- ------------------ 117.0/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 117.2/216.1 MB 6.2 MB/s eta 0:00:17\n", "   --------------------- ------------------ 117.3/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 117.8/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 118.1/216.1 MB 6.1 MB/s eta 0:00:17\n", "   --------------------- ------------------ 118.4/216.1 MB 6.1 MB/s eta 0:00:16\n", "   --------------------- ------------------ 118.7/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 119.0/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 119.3/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 119.5/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 119.8/216.1 MB 6.3 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 120.1/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 120.4/216.1 MB 6.3 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 120.7/216.1 MB 6.1 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 121.0/216.1 MB 6.1 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 121.3/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 121.5/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 121.8/216.1 MB 6.2 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 122.1/216.1 MB 6.1 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 122.4/216.1 MB 6.1 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 122.6/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ---------------------- ----------------- 123.0/216.1 MB 6.1 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 123.2/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ---------------------- ----------------- 123.5/216.1 MB 6.1 MB/s eta 0:00:16\n", "   ---------------------- ----------------- 123.8/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ---------------------- ----------------- 124.1/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 124.4/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 124.7/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 124.9/216.1 MB 6.1 MB/s eta 0:00:16\n", "   ----------------------- ---------------- 125.3/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 125.6/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 126.0/216.1 MB 6.4 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 126.2/216.1 MB 6.3 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 126.6/216.1 MB 6.3 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 126.8/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 127.1/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 127.3/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 127.6/216.1 MB 6.3 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 127.9/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 128.2/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 128.6/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ----------------------- ---------------- 128.8/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ----------------------- ---------------- 129.1/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ----------------------- ---------------- 129.3/216.1 MB 6.3 MB/s eta 0:00:14\n", "   ----------------------- ---------------- 129.6/216.1 MB 6.2 MB/s eta 0:00:15\n", "   ------------------------ --------------- 129.9/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 130.2/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 130.4/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 130.6/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 131.0/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 131.3/216.1 MB 6.1 MB/s eta 0:00:14\n", "   ------------------------ --------------- 131.6/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 131.8/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 132.2/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 132.5/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 132.8/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 133.1/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 133.3/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 133.7/216.1 MB 6.3 MB/s eta 0:00:14\n", "   ------------------------ --------------- 134.0/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 134.2/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 134.4/216.1 MB 6.3 MB/s eta 0:00:13\n", "   ------------------------ --------------- 134.8/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------ --------------- 135.0/216.1 MB 6.1 MB/s eta 0:00:14\n", "   ------------------------- -------------- 135.4/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------- -------------- 135.6/216.1 MB 6.2 MB/s eta 0:00:14\n", "   ------------------------- -------------- 135.8/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 136.1/216.1 MB 6.1 MB/s eta 0:00:14\n", "   ------------------------- -------------- 136.4/216.1 MB 6.1 MB/s eta 0:00:14\n", "   ------------------------- -------------- 136.7/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 137.1/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 137.2/216.1 MB 6.1 MB/s eta 0:00:13\n", "   ------------------------- -------------- 137.6/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 137.8/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 138.1/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 138.4/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 138.8/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 139.0/216.1 MB 6.1 MB/s eta 0:00:13\n", "   ------------------------- -------------- 139.2/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 139.6/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 139.9/216.1 MB 6.3 MB/s eta 0:00:13\n", "   ------------------------- -------------- 140.2/216.1 MB 6.2 MB/s eta 0:00:13\n", "   ------------------------- -------------- 140.3/216.1 MB 6.1 MB/s eta 0:00:13\n", "   -------------------------- ------------- 140.7/216.1 MB 6.2 MB/s eta 0:00:13\n", "   -------------------------- ------------- 140.9/216.1 MB 6.3 MB/s eta 0:00:12\n", "   -------------------------- ------------- 141.3/216.1 MB 6.4 MB/s eta 0:00:12\n", "   -------------------------- ------------- 141.6/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 141.8/216.1 MB 6.2 MB/s eta 0:00:13\n", "   -------------------------- ------------- 142.1/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 142.4/216.1 MB 6.1 MB/s eta 0:00:13\n", "   -------------------------- ------------- 142.7/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 143.0/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 143.3/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 143.6/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 143.8/216.1 MB 6.1 MB/s eta 0:00:12\n", "   -------------------------- ------------- 144.1/216.1 MB 6.1 MB/s eta 0:00:12\n", "   -------------------------- ------------- 144.4/216.1 MB 6.1 MB/s eta 0:00:12\n", "   -------------------------- ------------- 144.6/216.1 MB 6.1 MB/s eta 0:00:12\n", "   -------------------------- ------------- 145.0/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 145.2/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 145.6/216.1 MB 6.2 MB/s eta 0:00:12\n", "   -------------------------- ------------- 145.8/216.1 MB 6.2 MB/s eta 0:00:12\n", "   --------------------------- ------------ 146.1/216.1 MB 6.2 MB/s eta 0:00:12\n", "   --------------------------- ------------ 146.4/216.1 MB 6.2 MB/s eta 0:00:12\n", "   --------------------------- ------------ 146.8/216.1 MB 6.2 MB/s eta 0:00:12\n", "   --------------------------- ------------ 147.0/216.1 MB 6.2 MB/s eta 0:00:12\n", "   --------------------------- ------------ 147.3/216.1 MB 6.3 MB/s eta 0:00:11\n", "   --------------------------- ------------ 147.5/216.1 MB 6.2 MB/s eta 0:00:12\n", "   --------------------------- ------------ 147.8/216.1 MB 6.2 MB/s eta 0:00:11\n", "   --------------------------- ------------ 148.1/216.1 MB 6.2 MB/s eta 0:00:11\n", "   --------------------------- ------------ 148.3/216.1 MB 6.1 MB/s eta 0:00:12\n", "   --------------------------- ------------ 148.7/216.1 MB 6.2 MB/s eta 0:00:11\n", "   --------------------------- ------------ 149.1/216.1 MB 6.2 MB/s eta 0:00:11\n", "   --------------------------- ------------ 149.3/216.1 MB 6.3 MB/s eta 0:00:11\n", "   --------------------------- ------------ 149.6/216.1 MB 6.2 MB/s eta 0:00:11\n", "   --------------------------- ------------ 149.9/216.1 MB 6.2 MB/s eta 0:00:11\n", "   --------------------------- ------------ 150.2/216.1 MB 6.3 MB/s eta 0:00:11\n", "   --------------------------- ------------ 150.5/216.1 MB 6.3 MB/s eta 0:00:11\n", "   --------------------------- ------------ 150.7/216.1 MB 6.2 MB/s eta 0:00:11\n", "   --------------------------- ------------ 151.0/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 151.3/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 151.6/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 151.9/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 152.2/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 152.4/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 152.7/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 153.0/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 153.3/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 153.6/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 153.8/216.1 MB 6.2 MB/s eta 0:00:11\n", "   ---------------------------- ----------- 154.1/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 154.4/216.1 MB 6.4 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 154.7/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 155.0/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 155.3/216.1 MB 6.3 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 155.6/216.1 MB 6.3 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 155.9/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 156.1/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 156.4/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ---------------------------- ----------- 156.6/216.1 MB 6.1 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 156.9/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 157.3/216.1 MB 6.3 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 157.5/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 157.7/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 158.0/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 158.3/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 158.8/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 159.0/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 159.1/216.1 MB 6.1 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 159.4/216.1 MB 6.0 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 159.6/216.1 MB 6.0 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.1/216.1 MB 6.1 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.4/216.1 MB 6.2 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.5/216.1 MB 6.1 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.6/216.1 MB 5.9 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.6/216.1 MB 5.9 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.6/216.1 MB 5.7 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.6/216.1 MB 5.5 MB/s eta 0:00:10\n", "   ----------------------------- ---------- 160.8/216.1 MB 5.4 MB/s eta 0:00:11\n", "   ----------------------------- ---------- 161.1/216.1 MB 5.4 MB/s eta 0:00:11\n", "   ----------------------------- ---------- 161.3/216.1 MB 5.4 MB/s eta 0:00:11\n", "   ----------------------------- ---------- 161.6/216.1 MB 5.4 MB/s eta 0:00:11\n", "   ----------------------------- ---------- 161.9/216.1 MB 5.4 MB/s eta 0:00:11\n", "   ------------------------------ --------- 162.2/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 162.5/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 162.7/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 163.0/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 163.2/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 163.5/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 163.8/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 164.1/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 164.4/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 164.6/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 164.9/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 165.2/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 165.5/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 165.8/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 166.1/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 166.4/216.1 MB 5.5 MB/s eta 0:00:10\n", "   ------------------------------ --------- 166.8/216.1 MB 5.4 MB/s eta 0:00:10\n", "   ------------------------------ --------- 167.2/216.1 MB 5.5 MB/s eta 0:00:09\n", "   ------------------------------- -------- 167.7/216.1 MB 5.6 MB/s eta 0:00:09\n", "   ------------------------------- -------- 168.1/216.1 MB 5.7 MB/s eta 0:00:09\n", "   ------------------------------- -------- 168.6/216.1 MB 5.8 MB/s eta 0:00:09\n", "   ------------------------------- -------- 169.0/216.1 MB 5.9 MB/s eta 0:00:08\n", "   ------------------------------- -------- 169.7/216.1 MB 6.4 MB/s eta 0:00:08\n", "   ------------------------------- -------- 170.0/216.1 MB 6.3 MB/s eta 0:00:08\n", "   ------------------------------- -------- 170.3/216.1 MB 6.2 MB/s eta 0:00:08\n", "   ------------------------------- -------- 170.4/216.1 MB 6.1 MB/s eta 0:00:08\n", "   ------------------------------- -------- 170.7/216.1 MB 6.1 MB/s eta 0:00:08\n", "   ------------------------------- -------- 171.2/216.1 MB 7.1 MB/s eta 0:00:07\n", "   ------------------------------- -------- 171.5/216.1 MB 7.1 MB/s eta 0:00:07\n", "   ------------------------------- -------- 171.8/216.1 MB 7.1 MB/s eta 0:00:07\n", "   ------------------------------- -------- 172.1/216.1 MB 7.1 MB/s eta 0:00:07\n", "   ------------------------------- -------- 172.3/216.1 MB 7.1 MB/s eta 0:00:07\n", "   ------------------------------- -------- 172.6/216.1 MB 7.1 MB/s eta 0:00:07\n", "   -------------------------------- ------- 172.9/216.1 MB 7.1 MB/s eta 0:00:07\n", "   -------------------------------- ------- 173.2/216.1 MB 7.1 MB/s eta 0:00:07\n", "   -------------------------------- ------- 173.4/216.1 MB 7.1 MB/s eta 0:00:06\n", "   -------------------------------- ------- 173.7/216.1 MB 7.1 MB/s eta 0:00:06\n", "   -------------------------------- ------- 174.0/216.1 MB 7.1 MB/s eta 0:00:06\n", "   -------------------------------- ------- 174.2/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 174.5/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 174.8/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 175.1/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 175.3/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 175.6/216.1 MB 7.1 MB/s eta 0:00:06\n", "   -------------------------------- ------- 175.9/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 176.1/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 176.4/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 176.7/216.1 MB 7.0 MB/s eta 0:00:06\n", "   -------------------------------- ------- 176.8/216.1 MB 6.9 MB/s eta 0:00:06\n", "   -------------------------------- ------- 176.8/216.1 MB 6.9 MB/s eta 0:00:06\n", "   -------------------------------- ------- 177.3/216.1 MB 6.6 MB/s eta 0:00:06\n", "   -------------------------------- ------- 177.5/216.1 MB 6.5 MB/s eta 0:00:06\n", "   -------------------------------- ------- 177.8/216.1 MB 6.5 MB/s eta 0:00:06\n", "   -------------------------------- ------- 178.2/216.1 MB 6.5 MB/s eta 0:00:06\n", "   --------------------------------- ------ 178.5/216.1 MB 6.4 MB/s eta 0:00:06\n", "   --------------------------------- ------ 178.9/216.1 MB 6.4 MB/s eta 0:00:06\n", "   --------------------------------- ------ 179.3/216.1 MB 6.4 MB/s eta 0:00:06\n", "   --------------------------------- ------ 179.8/216.1 MB 6.3 MB/s eta 0:00:06\n", "   --------------------------------- ------ 180.0/216.1 MB 6.3 MB/s eta 0:00:06\n", "   --------------------------------- ------ 180.2/216.1 MB 6.1 MB/s eta 0:00:06\n", "   --------------------------------- ------ 180.4/216.1 MB 6.1 MB/s eta 0:00:06\n", "   --------------------------------- ------ 180.6/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 181.2/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 181.5/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 181.8/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 182.1/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 182.4/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 182.6/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 182.9/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 183.1/216.1 MB 6.2 MB/s eta 0:00:06\n", "   --------------------------------- ------ 183.4/216.1 MB 6.2 MB/s eta 0:00:06\n", "   ---------------------------------- ----- 183.7/216.1 MB 6.2 MB/s eta 0:00:06\n", "   ---------------------------------- ----- 184.0/216.1 MB 6.2 MB/s eta 0:00:06\n", "   ---------------------------------- ----- 184.3/216.1 MB 6.2 MB/s eta 0:00:06\n", "   ---------------------------------- ----- 184.5/216.1 MB 6.2 MB/s eta 0:00:06\n", "   ---------------------------------- ----- 184.8/216.1 MB 6.3 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 185.1/216.1 MB 6.2 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 185.4/216.1 MB 6.2 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 185.5/216.1 MB 6.2 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 185.9/216.1 MB 6.3 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 186.2/216.1 MB 6.2 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 186.5/216.1 MB 6.3 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 186.8/216.1 MB 6.3 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 187.0/216.1 MB 6.3 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 187.3/216.1 MB 6.4 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 187.6/216.1 MB 6.5 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 187.9/216.1 MB 6.5 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 188.3/216.1 MB 6.5 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 188.6/216.1 MB 6.5 MB/s eta 0:00:05\n", "   ---------------------------------- ----- 188.7/216.1 MB 6.4 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 189.1/216.1 MB 6.4 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 189.3/216.1 MB 6.2 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 189.4/216.1 MB 6.2 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 190.1/216.1 MB 6.2 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 190.3/216.1 MB 6.4 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 190.6/216.1 MB 6.3 MB/s eta 0:00:05\n", "   ----------------------------------- ---- 190.9/216.1 MB 6.4 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 191.2/216.1 MB 6.3 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 191.5/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 191.7/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 192.0/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 192.3/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 192.6/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 192.8/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 193.1/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 193.5/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 193.8/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 194.1/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ----------------------------------- ---- 194.4/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 194.6/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 194.9/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 195.2/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 195.6/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 196.0/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 196.3/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 196.6/216.1 MB 6.3 MB/s eta 0:00:04\n", "   ------------------------------------ --- 196.9/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 197.0/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 197.3/216.1 MB 6.2 MB/s eta 0:00:04\n", "   ------------------------------------ --- 197.6/216.1 MB 6.3 MB/s eta 0:00:03\n", "   ------------------------------------ --- 197.9/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------ --- 198.2/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------ --- 198.5/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------ --- 198.9/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------ --- 199.2/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------ --- 199.4/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------ --- 199.7/216.1 MB 6.4 MB/s eta 0:00:03\n", "   ------------------------------------- -- 200.0/216.1 MB 6.3 MB/s eta 0:00:03\n", "   ------------------------------------- -- 200.2/216.1 MB 6.1 MB/s eta 0:00:03\n", "   ------------------------------------- -- 200.5/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 200.8/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 201.1/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 201.4/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 201.6/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 201.8/216.1 MB 6.1 MB/s eta 0:00:03\n", "   ------------------------------------- -- 202.2/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 202.6/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 202.9/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 203.1/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 203.4/216.1 MB 6.2 MB/s eta 0:00:03\n", "   ------------------------------------- -- 203.7/216.1 MB 6.2 MB/s eta 0:00:02\n", "   ------------------------------------- -- 204.0/216.1 MB 6.2 MB/s eta 0:00:02\n", "   ------------------------------------- -- 204.3/216.1 MB 6.2 MB/s eta 0:00:02\n", "   ------------------------------------- -- 204.6/216.1 MB 6.2 MB/s eta 0:00:02\n", "   ------------------------------------- -- 204.8/216.1 MB 6.2 MB/s eta 0:00:02\n", "   ------------------------------------- -- 205.1/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 205.4/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 205.7/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 206.0/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 206.2/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 206.5/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 206.8/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 207.1/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 207.3/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 207.6/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 207.9/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 208.2/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 208.5/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 208.8/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 209.1/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 209.4/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 209.6/216.1 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------------- - 210.0/216.1 MB 6.3 MB/s eta 0:00:01\n", "   -------------------------------------- - 210.2/216.1 MB 6.1 MB/s eta 0:00:01\n", "   -------------------------------------- - 210.4/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  210.8/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  211.0/216.1 MB 6.1 MB/s eta 0:00:01\n", "   ---------------------------------------  211.4/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  211.5/216.1 MB 6.1 MB/s eta 0:00:01\n", "   ---------------------------------------  211.8/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  212.2/216.1 MB 6.3 MB/s eta 0:00:01\n", "   ---------------------------------------  212.5/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  212.7/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  213.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  213.4/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  213.6/216.1 MB 6.1 MB/s eta 0:00:01\n", "   ---------------------------------------  213.8/216.1 MB 6.1 MB/s eta 0:00:01\n", "   ---------------------------------------  214.1/216.1 MB 6.1 MB/s eta 0:00:01\n", "   ---------------------------------------  214.5/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  214.7/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  215.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  215.4/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  215.5/216.1 MB 6.1 MB/s eta 0:00:01\n", "   ---------------------------------------  215.8/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  216.0/216.1 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 216.1/216.1 MB 4.5 MB/s eta 0:00:00\n", "Downloading transformers-4.53.1-py3-none-any.whl (10.8 MB)\n", "   ---------------------------------------- 0.0/10.8 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/10.8 MB 8.3 MB/s eta 0:00:02\n", "   -- ------------------------------------- 0.6/10.8 MB 8.0 MB/s eta 0:00:02\n", "   --- ------------------------------------ 1.0/10.8 MB 7.9 MB/s eta 0:00:02\n", "   ---- ----------------------------------- 1.4/10.8 MB 7.8 MB/s eta 0:00:02\n", "   ------ --------------------------------- 1.8/10.8 MB 8.2 MB/s eta 0:00:02\n", "   -------- ------------------------------- 2.2/10.8 MB 8.4 MB/s eta 0:00:02\n", "   ---------- ----------------------------- 2.8/10.8 MB 8.8 MB/s eta 0:00:01\n", "   ----------- ---------------------------- 3.2/10.8 MB 9.0 MB/s eta 0:00:01\n", "   ------------- -------------------------- 3.8/10.8 MB 9.2 MB/s eta 0:00:01\n", "   ---------------- ----------------------- 4.4/10.8 MB 9.7 MB/s eta 0:00:01\n", "   ---------------- ----------------------- 4.5/10.8 MB 9.3 MB/s eta 0:00:01\n", "   ----------------- ---------------------- 4.7/10.8 MB 9.1 MB/s eta 0:00:01\n", "   ------------------ --------------------- 4.9/10.8 MB 8.8 MB/s eta 0:00:01\n", "   ------------------- -------------------- 5.2/10.8 MB 8.4 MB/s eta 0:00:01\n", "   --------------------- ------------------ 5.9/10.8 MB 8.7 MB/s eta 0:00:01\n", "   ---------------------- ----------------- 6.2/10.8 MB 8.5 MB/s eta 0:00:01\n", "   ----------------------- ---------------- 6.4/10.8 MB 8.4 MB/s eta 0:00:01\n", "   ------------------------ --------------- 6.7/10.8 MB 8.3 MB/s eta 0:00:01\n", "   ------------------------- -------------- 7.0/10.8 MB 8.2 MB/s eta 0:00:01\n", "   -------------------------- ------------- 7.3/10.8 MB 8.0 MB/s eta 0:00:01\n", "   --------------------------- ------------ 7.5/10.8 MB 7.9 MB/s eta 0:00:01\n", "   ----------------------------- ---------- 7.9/10.8 MB 7.9 MB/s eta 0:00:01\n", "   ------------------------------ --------- 8.2/10.8 MB 7.8 MB/s eta 0:00:01\n", "   ------------------------------- -------- 8.4/10.8 MB 7.7 MB/s eta 0:00:01\n", "   -------------------------------- ------- 8.7/10.8 MB 7.6 MB/s eta 0:00:01\n", "   --------------------------------- ------ 9.0/10.8 MB 7.6 MB/s eta 0:00:01\n", "   ---------------------------------- ----- 9.3/10.8 MB 7.5 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 9.6/10.8 MB 7.5 MB/s eta 0:00:01\n", "   ------------------------------------ --- 9.9/10.8 MB 7.4 MB/s eta 0:00:01\n", "   ------------------------------------- -- 10.2/10.8 MB 7.4 MB/s eta 0:00:01\n", "   -------------------------------------- - 10.4/10.8 MB 7.3 MB/s eta 0:00:01\n", "   ---------------------------------------  10.7/10.8 MB 7.3 MB/s eta 0:00:01\n", "   ---------------------------------------  10.8/10.8 MB 7.3 MB/s eta 0:00:01\n", "   ---------------------------------------- 10.8/10.8 MB 7.1 MB/s eta 0:00:00\n", "Downloading safetensors-0.5.3-cp38-abi3-win_amd64.whl (308 kB)\n", "   ---------------------------------------- 0.0/308.9 kB ? eta -:--:--\n", "   ---------------------------------------  307.2/308.9 kB 6.3 MB/s eta 0:00:01\n", "   ---------------------------------------- 308.9/308.9 kB 4.7 MB/s eta 0:00:00\n", "Downloading sympy-1.14.0-py3-none-any.whl (6.3 MB)\n", "   ---------------------------------------- 0.0/6.3 MB ? eta -:--:--\n", "   - -------------------------------------- 0.3/6.3 MB 7.9 MB/s eta 0:00:01\n", "   --- ------------------------------------ 0.5/6.3 MB 6.7 MB/s eta 0:00:01\n", "   ----- ---------------------------------- 0.8/6.3 MB 6.4 MB/s eta 0:00:01\n", "   ------- -------------------------------- 1.1/6.3 MB 6.5 MB/s eta 0:00:01\n", "   --------- ------------------------------ 1.4/6.3 MB 6.5 MB/s eta 0:00:01\n", "   ----------- ---------------------------- 1.8/6.3 MB 6.7 MB/s eta 0:00:01\n", "   ------------- -------------------------- 2.1/6.3 MB 7.0 MB/s eta 0:00:01\n", "   ------------- -------------------------- 2.1/6.3 MB 7.0 MB/s eta 0:00:01\n", "   ------------- -------------------------- 2.1/6.3 MB 7.0 MB/s eta 0:00:01\n", "   -------------- ------------------------- 2.4/6.3 MB 5.6 MB/s eta 0:00:01\n", "   -------------- ------------------------- 2.4/6.3 MB 5.6 MB/s eta 0:00:01\n", "   -------------- ------------------------- 2.4/6.3 MB 5.6 MB/s eta 0:00:01\n", "   --------------- ------------------------ 2.4/6.3 MB 4.1 MB/s eta 0:00:01\n", "   --------------- ------------------------ 2.4/6.3 MB 4.1 MB/s eta 0:00:01\n", "   --------------- ------------------------ 2.4/6.3 MB 3.5 MB/s eta 0:00:02\n", "   --------------- ------------------------ 2.4/6.3 MB 3.5 MB/s eta 0:00:02\n", "   ----------------- ---------------------- 2.7/6.3 MB 3.6 MB/s eta 0:00:01\n", "   ----------------- ---------------------- 2.7/6.3 MB 3.6 MB/s eta 0:00:01\n", "   -------------------- ------------------- 3.2/6.3 MB 3.8 MB/s eta 0:00:01\n", "   --------------------- ------------------ 3.5/6.3 MB 3.9 MB/s eta 0:00:01\n", "   --------------------- ------------------ 3.5/6.3 MB 3.9 MB/s eta 0:00:01\n", "   -------------------------- ------------- 4.2/6.3 MB 4.2 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 4.5/6.3 MB 4.2 MB/s eta 0:00:01\n", "   ------------------------------ --------- 4.8/6.3 MB 4.4 MB/s eta 0:00:01\n", "   --------------------------------- ------ 5.2/6.3 MB 4.6 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 5.6/6.3 MB 4.7 MB/s eta 0:00:01\n", "   -------------------------------------- - 6.1/6.3 MB 4.9 MB/s eta 0:00:01\n", "   ---------------------------------------- 6.3/6.3 MB 4.9 MB/s eta 0:00:00\n", "Downloading tokenizers-0.21.2-cp39-abi3-win_amd64.whl (2.5 MB)\n", "   ---------------------------------------- 0.0/2.5 MB ? eta -:--:--\n", "   ---------- ----------------------------- 0.6/2.5 MB 13.5 MB/s eta 0:00:01\n", "   ----------------- ---------------------- 1.1/2.5 MB 11.8 MB/s eta 0:00:01\n", "   ------------------------- -------------- 1.6/2.5 MB 11.3 MB/s eta 0:00:01\n", "   --------------------------------- ------ 2.1/2.5 MB 11.2 MB/s eta 0:00:01\n", "   ---------------------------------------  2.5/2.5 MB 11.5 MB/s eta 0:00:01\n", "   ---------------------------------------- 2.5/2.5 MB 10.0 MB/s eta 0:00:00\n", "Installing collected packages: ltp-extension, sympy, safetensors, torch, huggingface-hub, tokenizers, transformers, ltp-core, ltp\n", "  Attempting uninstall: sympy\n", "    Found existing installation: sympy 1.12\n", "    Uninstalling sympy-1.12:\n", "      Successfully uninstalled sympy-1.12\n", "Successfully installed huggingface-hub-0.33.2 ltp-4.2.14 ltp-core-0.1.4 ltp-extension-0.1.13 safetensors-0.5.3 sympy-1.14.0 tokenizers-0.21.2 torch-2.7.1 transformers-4.53.1\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install ltp"]}, {"cell_type": "code", "execution_count": null, "id": "0cf72c0c-441f-455a-b6d7-4979ab528065", "metadata": {}, "outputs": [], "source": ["jupyter nbextension enable --py widgetsnbextension\n", "jupyter notebook"]}, {"cell_type": "code", "execution_count": 15, "id": "8c2d8d42-3ed7-4cfc-be4a-aeb68ea1a762", "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'LTP' object has no attribute 'seg'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[15], line 44\u001b[0m\n\u001b[0;32m     42\u001b[0m subjs, verbs, objs, results \u001b[38;5;241m=\u001b[39m [], [], [], []\n\u001b[0;32m     43\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m text \u001b[38;5;129;01min\u001b[39;00m df[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m事件\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n\u001b[1;32m---> 44\u001b[0m     subj, verb, obj \u001b[38;5;241m=\u001b[39m extract_svo_ltp(\u001b[38;5;28mstr\u001b[39m(text))\n\u001b[0;32m     45\u001b[0m     result \u001b[38;5;241m=\u001b[39m classify_event(subj, obj)\n\u001b[0;32m     46\u001b[0m     subjs\u001b[38;5;241m.\u001b[39mappend(subj)\n", "Cell \u001b[1;32mIn[15], line 12\u001b[0m, in \u001b[0;36mextract_svo_ltp\u001b[1;34m(sentence)\u001b[0m\n\u001b[0;32m     11\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mextract_svo_ltp\u001b[39m(sentence):\n\u001b[1;32m---> 12\u001b[0m     seg, hidden \u001b[38;5;241m=\u001b[39m ltp\u001b[38;5;241m.\u001b[39mseg([sentence])\n\u001b[0;32m     13\u001b[0m     dep \u001b[38;5;241m=\u001b[39m ltp\u001b[38;5;241m.\u001b[39mdep(hidden)[\u001b[38;5;241m0\u001b[39m]  \u001b[38;5;66;03m# 依存句法\u001b[39;00m\n\u001b[0;32m     14\u001b[0m     words \u001b[38;5;241m=\u001b[39m seg[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[1;32mD:\\anaconda\\anaconda\\Lib\\site-packages\\torch\\nn\\modules\\module.py:1940\u001b[0m, in \u001b[0;36mModule.__getattr__\u001b[1;34m(self, name)\u001b[0m\n\u001b[0;32m   1938\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m name \u001b[38;5;129;01min\u001b[39;00m modules:\n\u001b[0;32m   1939\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m modules[name]\n\u001b[1;32m-> 1940\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\n\u001b[0;32m   1941\u001b[0m     \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mtype\u001b[39m(\u001b[38;5;28mself\u001b[39m)\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m object has no attribute \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mname\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m   1942\u001b[0m )\n", "\u001b[1;31mAttributeError\u001b[0m: 'LTP' object has no attribute 'seg'"]}], "source": ["from ltp import LTP\n", "import pandas as pd\n", "\n", "# 初始化 LTP 模型\n", "ltp = LTP()\n", "\n", "# 群体词典（可自行扩展）\n", "dominant_groups = {\"城管\", \"警察\", \"老师\", \"教育局\", \"政府\", \"学校\", \"辅导员\", \"教官\", \"保安\"}\n", "vulnerable_groups = {\"小贩\", \"学生\", \"摊贩\", \"老人\", \"家长\", \"孩子\", \"女生\", \"男生\", \"儿童\", \"弱势群体\"}\n", "\n", "def extract_svo_ltp(sentence):\n", "    seg, hidden = ltp.seg([sentence])\n", "    dep = ltp.dep(hidden)[0]  # 依存句法\n", "    words = seg[0]\n", "    subj, verb, obj = None, None, None\n", "    \n", "    for i, (rel, head) in enumerate(dep):\n", "        if rel == \"SBV\":  # 主谓关系\n", "            subj = words[i]\n", "            verb = words[head - 1]  # LTP下标从1开始\n", "        elif rel == \"VOB\" and head - 1 < len(words):  # 动宾关系\n", "            if words[head - 1] == verb:\n", "                obj = words[i]\n", "    \n", "    return subj, verb, obj\n", "\n", "def classify_event(subj, obj):\n", "    if not subj or not obj:\n", "        return \"❌ 无法识别\"\n", "    if subj in dominant_groups and obj in vulnerable_groups:\n", "        return \"✅ 强势群体欺凌弱势群体\"\n", "    elif subj in vulnerable_groups and obj in dominant_groups:\n", "        return \"⚠️ 弱势群体攻击强势群体\"\n", "    else:\n", "        return \"❌ 无明显强弱关系\"\n", "\n", "# 读取数据\n", "input_file = \"300事件.xlsx\"\n", "df = pd.read_excel(input_file)\n", "\n", "# 对每条事件进行分析\n", "subjs, verbs, objs, results = [], [], [], []\n", "for text in df[\"事件\"]:\n", "    subj, verb, obj = extract_svo_ltp(str(text))\n", "    result = classify_event(subj, obj)\n", "    subjs.append(subj)\n", "    verbs.append(verb)\n", "    objs.append(obj)\n", "    results.append(result)\n", "\n", "# 添加结果列\n", "df[\"主语\"] = subjs\n", "df[\"动词\"] = verbs\n", "df[\"宾语\"] = objs\n", "df[\"识别结果\"] = results\n", "\n", "# 输出文件\n", "output_file = \"事件识别结果_ltp.xlsx\"\n", "df.to_excel(output_file, index=False)\n", "print(f\"✅ 分析完成，结果已输出至：{output_file}\")\n"]}, {"cell_type": "code", "execution_count": 9, "id": "d0c7ab98-2b38-4a92-9906-b69f33cccc97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: ltp in d:\\anaconda\\anaconda\\lib\\site-packages (4.2.14)\n", "Requirement already satisfied: openpyxl in d:\\anaconda\\anaconda\\lib\\site-packages (3.1.2)\n", "Requirement already satisfied: jieba in d:\\anaconda\\anaconda\\lib\\site-packages (0.42.1)\n", "Requirement already satisfied: ltp-core>=0.1.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp) (0.1.4)\n", "Requirement already satisfied: ltp-extension>=0.1.9 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp) (0.1.13)\n", "Requirement already satisfied: huggingface-hub>=0.8.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp) (0.33.2)\n", "Requirement already satisfied: et-xmlfile in d:\\anaconda\\anaconda\\lib\\site-packages (from openpyxl) (1.1.0)\n", "Requirement already satisfied: filelock in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (3.13.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (2024.3.1)\n", "Requirement already satisfied: packaging>=20.9 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (23.2)\n", "Requirement already satisfied: pyyaml>=5.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (6.0.1)\n", "Requirement already satisfied: requests in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (2.32.2)\n", "Requirement already satisfied: tqdm>=4.42.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (4.66.4)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (4.13.2)\n", "Requirement already satisfied: torch>=1.6.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp-core>=0.1.3->ltp) (2.7.1)\n", "Requirement already satisfied: transformers>=4.0.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp-core>=0.1.3->ltp) (4.53.1)\n", "Requirement already satisfied: sympy>=1.13.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (1.14.0)\n", "Requirement already satisfied: networkx in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (3.2.1)\n", "Requirement already satisfied: jinja2 in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (3.1.4)\n", "Requirement already satisfied: setuptools in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (69.5.1)\n", "Requirement already satisfied: colorama in d:\\anaconda\\anaconda\\lib\\site-packages (from tqdm>=4.42.1->huggingface-hub>=0.8.0->ltp) (0.4.6)\n", "Requirement already satisfied: numpy>=1.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (1.26.4)\n", "Requirement already satisfied: regex!=2019.12.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (2023.10.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (0.21.2)\n", "Requirement already satisfied: safetensors>=0.4.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (0.5.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2025.6.15)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from sympy>=1.13.3->torch>=1.6.0->ltp-core>=0.1.3->ltp) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from jinja2->torch>=1.6.0->ltp-core>=0.1.3->ltp) (2.1.3)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install ltp openpyxl jieba"]}, {"cell_type": "code", "execution_count": 13, "id": "ca15ed6b-31ab-4460-a51f-1892d80e4be3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 分析完成，结果已保存为：事件群体分析结果.xlsx\n"]}], "source": ["import pandas as pd\n", "from ltp import LTP\n", "from ltp import StnSplit\n", "\n", "# 初始化模型\n", "ltp = LTP()\n", "splitter = StnSplit()\n", "\n", "# 群体词典\n", "strong_groups = {\"城管\", \"警察\", \"老师\", \"教官\", \"政府\", \"保安\", \"医生\", \"法官\", \"班主任\"}\n", "weak_groups = {\"学生\", \"小贩\", \"摊贩\", \"市民\", \"孩子\", \"女孩\", \"老人\", \"男童\", \"女童\", \"百姓\", \"居民\"}\n", "\n", "# 读取 Excel 文件\n", "df = pd.read_excel(\"300事件.xlsx\")\n", "event_list = df[\"事件\"].dropna().astype(str).tolist()\n", "\n", "# 结果列表\n", "results = []\n", "\n", "# 主函数：处理每个事件句子\n", "def process_text(text):\n", "    try:\n", "        # 分句\n", "        sentences = splitter.split(text)\n", "        for sent in sentences:\n", "            # pipeline 获取多任务结果\n", "            result = ltp.pipeline([sent], tasks=[\"cws\", \"pos\", \"dep\"], return_dict=True)\n", "\n", "            words = result[\"cws\"][0]\n", "            pos = result[\"pos\"][0]\n", "            dep = result[\"dep\"][0]  # 格式为 (head, relation)\n", "\n", "            subj = \"\"\n", "            verb = \"\"\n", "            obj = \"\"\n", "\n", "            for i, (head, rel) in enumerate(dep):\n", "                if rel == \"SBV\":  # 主谓\n", "                    subj = words[i]\n", "                    if head > 0:\n", "                        verb = words[head - 1]\n", "                elif rel == \"VOB\":  # 动宾\n", "                    obj = words[i]\n", "\n", "            if subj in strong_groups and obj in weak_groups:\n", "                conclusion = f\"{subj}欺凌{obj}（{subj}为强势群体）\"\n", "            elif subj in weak_groups and obj in strong_groups:\n", "                conclusion = f\"{subj}反抗{obj}（{obj}为强势群体）\"\n", "            elif subj and obj:\n", "                conclusion = f\"{subj} vs {obj}（无明显强弱关系）\"\n", "            else:\n", "                conclusion = \"信息不足，无法判断\"\n", "\n", "            results.append({\n", "                \"原句\": sent,\n", "                \"主语\": subj,\n", "                \"谓语\": verb,\n", "                \"宾语\": obj,\n", "                \"分析结果\": conclusion\n", "            })\n", "    except Exception as e:\n", "        results.append({\n", "            \"原句\": text,\n", "            \"主语\": \"\",\n", "            \"谓语\": \"\",\n", "            \"宾语\": \"\",\n", "            \"分析结果\": f\"分析失败：{str(e)}\"\n", "        })\n", "\n", "# 批量处理\n", "for event in event_list:\n", "    process_text(event)\n", "\n", "# 保存结果\n", "result_df = pd.DataFrame(results)\n", "result_df.to_excel(\"事件群体分析结果.xlsx\", index=False)\n", "\n", "print(\"✅ 分析完成，结果已保存为：事件群体分析结果.xlsx\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "936fd6b1-88d4-4c7e-87c9-b83f2160fcf8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: ltp in d:\\anaconda\\anaconda\\lib\\site-packages (4.2.14)\n", "Requirement already satisfied: pandas in d:\\anaconda\\anaconda\\lib\\site-packages (2.2.2)\n", "Requirement already satisfied: openpyxl in d:\\anaconda\\anaconda\\lib\\site-packages (3.1.2)\n", "Requirement already satisfied: ltp-core>=0.1.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp) (0.1.4)\n", "Requirement already satisfied: ltp-extension>=0.1.9 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp) (0.1.13)\n", "Requirement already satisfied: huggingface-hub>=0.8.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp) (0.33.2)\n", "Requirement already satisfied: numpy>=1.26.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from pandas) (1.26.4)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in d:\\anaconda\\anaconda\\lib\\site-packages (from pandas) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from pandas) (2024.1)\n", "Requirement already satisfied: tzdata>=2022.7 in d:\\anaconda\\anaconda\\lib\\site-packages (from pandas) (2023.3)\n", "Requirement already satisfied: et-xmlfile in d:\\anaconda\\anaconda\\lib\\site-packages (from openpyxl) (1.1.0)\n", "Requirement already satisfied: filelock in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (3.13.1)\n", "Requirement already satisfied: fsspec>=2023.5.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (2024.3.1)\n", "Requirement already satisfied: packaging>=20.9 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (23.2)\n", "Requirement already satisfied: pyyaml>=5.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (6.0.1)\n", "Requirement already satisfied: requests in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (2.32.2)\n", "Requirement already satisfied: tqdm>=4.42.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (4.66.4)\n", "Requirement already satisfied: typing-extensions>=3.7.4.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from huggingface-hub>=0.8.0->ltp) (4.13.2)\n", "Requirement already satisfied: torch>=1.6.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp-core>=0.1.3->ltp) (2.7.1)\n", "Requirement already satisfied: transformers>=4.0.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from ltp-core>=0.1.3->ltp) (4.53.1)\n", "Requirement already satisfied: six>=1.5 in d:\\anaconda\\anaconda\\lib\\site-packages (from python-dateutil>=2.8.2->pandas) (1.16.0)\n", "Requirement already satisfied: sympy>=1.13.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (1.14.0)\n", "Requirement already satisfied: networkx in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (3.2.1)\n", "Requirement already satisfied: jinja2 in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (3.1.4)\n", "Requirement already satisfied: setuptools in d:\\anaconda\\anaconda\\lib\\site-packages (from torch>=1.6.0->ltp-core>=0.1.3->ltp) (69.5.1)\n", "Requirement already satisfied: colorama in d:\\anaconda\\anaconda\\lib\\site-packages (from tqdm>=4.42.1->huggingface-hub>=0.8.0->ltp) (0.4.6)\n", "Requirement already satisfied: regex!=2019.12.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (2023.10.3)\n", "Requirement already satisfied: tokenizers<0.22,>=0.21 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (0.21.2)\n", "Requirement already satisfied: safetensors>=0.4.3 in d:\\anaconda\\anaconda\\lib\\site-packages (from transformers>=4.0.0->ltp-core>=0.1.3->ltp) (0.5.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2.0.4)\n", "Requirement already satisfied: idna<4,>=2.5 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in d:\\anaconda\\anaconda\\lib\\site-packages (from requests->huggingface-hub>=0.8.0->ltp) (2025.6.15)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from sympy>=1.13.3->torch>=1.6.0->ltp-core>=0.1.3->ltp) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in d:\\anaconda\\anaconda\\lib\\site-packages (from jinja2->torch>=1.6.0->ltp-core>=0.1.3->ltp) (2.1.3)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip install ltp pandas openpyxl"]}, {"cell_type": "code", "execution_count": 19, "id": "ded55a6a-553e-4ea9-ae0d-69d358274868", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "too many values to unpack (expected 2)", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[19], line 38\u001b[0m\n\u001b[0;32m     36\u001b[0m result \u001b[38;5;241m=\u001b[39m []\n\u001b[0;32m     37\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m evt \u001b[38;5;129;01min\u001b[39;00m events:\n\u001b[1;32m---> 38\u001b[0m     sub, pred, ob \u001b[38;5;241m=\u001b[39m extract_svo(evt)\n\u001b[0;32m     39\u001b[0m     result\u001b[38;5;241m.\u001b[39mappend([evt, sub, pred, ob])\n\u001b[0;32m     41\u001b[0m \u001b[38;5;66;03m# 写入Excel\u001b[39;00m\n", "Cell \u001b[1;32mIn[19], line 20\u001b[0m, in \u001b[0;36mextract_svo\u001b[1;34m(event)\u001b[0m\n\u001b[0;32m     16\u001b[0m deps \u001b[38;5;241m=\u001b[39m res[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdep\u001b[39m\u001b[38;5;124m\"\u001b[39m][\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m     18\u001b[0m subject \u001b[38;5;241m=\u001b[39m predicate \u001b[38;5;241m=\u001b[39m obj \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m---> 20\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m i, (head, relation) \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(deps):\n\u001b[0;32m     21\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m relation \u001b[38;5;241m==\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mSBV\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m     22\u001b[0m         subject \u001b[38;5;241m=\u001b[39m words[i]\n", "\u001b[1;31mValueError\u001b[0m: too many values to unpack (expected 2)"]}], "source": ["import pandas as pd\n", "from ltp import LTP\n", "\n", "# 读取Excel文件\n", "file_path = \"300事件.xlsx\"  # 确保此文件与.py或.ipynb在同一目录\n", "df = pd.read_excel(file_path)\n", "events = df[\"事件\"].dropna().tolist()\n", "\n", "# 初始化LTP\n", "ltp = LTP()\n", "\n", "# 主谓宾提取函数\n", "def extract_svo(event):\n", "    res = ltp.pipeline([event], tasks=[\"cws\", \"pos\", \"dep\"], return_dict=True)\n", "    words = res[\"cws\"][0]\n", "    deps = res[\"dep\"][0]\n", "\n", "    subject = predicate = obj = \"\"\n", "\n", "    for i, (head, relation) in enumerate(deps):\n", "        if relation == \"SBV\":\n", "            subject = words[i]\n", "            predicate = words[head - 1]\n", "        elif relation == \"VOB\":\n", "            predicate = words[head - 1]\n", "            obj = words[i]\n", "        elif relation == \"POB\" and words[head - 1] == \"被\":\n", "            # 被动语态处理：例如“他被打了” -> “某人打他”\n", "            predicate = words[i]\n", "            obj = words[head]\n", "            subject = \"未知\"\n", "\n", "    return subject, predicate, obj\n", "\n", "# 应用到所有事件\n", "result = []\n", "for evt in events:\n", "    sub, pred, ob = extract_svo(evt)\n", "    result.append([evt, sub, pred, ob])\n", "\n", "# 写入Excel\n", "df_result = pd.DataFrame(result, columns=[\"事件\", \"主语\", \"谓语\", \"宾语\"])\n", "df_result.to_excel(\"事件主谓宾分析结果.xlsx\", index=False)\n", "print(\"分析完成，文件已保存为：事件主谓宾分析结果.xlsx\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "842f565c-3ee3-40d7-9730-9152253009b5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}