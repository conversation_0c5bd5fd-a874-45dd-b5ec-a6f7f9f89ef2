pip install ltp
pip install ltp openpyxl jieba
pip install ltp pandas openpyxl
from ltp import LTP
import pandas as pd

# 初始化 LTP 模型
ltp = LTP()

# 群体词典（可自行扩展）
dominant_groups = {"城管", "警察", "老师", "教育局", "政府", "学校", "辅导员", "教官", "保安"}
vulnerable_groups = {"小贩", "学生", "摊贩", "老人", "家长", "孩子", "女生", "男生", "儿童", "弱势群体"}

def extract_svo_ltp(sentence):
    seg, hidden = ltp.seg([sentence])
    dep = ltp.dep(hidden)[0]  # 依存句法
    words = seg[0]
    subj, verb, obj = None, None, None
    
    for i, (rel, head) in enumerate(dep):
        if rel == "SBV":  # 主谓关系
            subj = words[i]
            verb = words[head - 1]  # LTP下标从1开始
        elif rel == "VOB" and head - 1 < len(words):  # 动宾关系
            if words[head - 1] == verb:
                obj = words[i]
    
    return subj, verb, obj

def classify_event(subj, obj):
    if not subj or not obj:
        return "❌ 无法识别"
    if subj in dominant_groups and obj in vulnerable_groups:
        return "✅ 强势群体欺凌弱势群体"
    elif subj in vulnerable_groups and obj in dominant_groups:
        return "⚠️ 弱势群体攻击强势群体"
    else:
        return "❌ 无明显强弱关系"

# 读取数据
input_file = "300事件.xlsx"
df = pd.read_excel(input_file)

# 对每条事件进行分析
subjs, verbs, objs, results = [], [], [], []
for text in df["事件"]:
    subj, verb, obj = extract_svo_ltp(str(text))
    result = classify_event(subj, obj)
    subjs.append(subj)
    verbs.append(verb)
    objs.append(obj)
    results.append(result)

# 添加结果列
df["主语"] = subjs
df["动词"] = verbs
df["宾语"] = objs
df["识别结果"] = results

# 输出文件
output_file = "事件识别结果_ltp.xlsx"
df.to_excel(output_file, index=False)
print(f"✅ 分析完成，结果已输出至：{output_file}")
import pandas as pd
from ltp import LTP
from ltp import StnSplit

# 初始化模型
ltp = LTP()
splitter = StnSplit()

# 群体词典
strong_groups = {"城管", "警察", "老师", "教官", "政府", "保安", "医生", "法官", "班主任"}
weak_groups = {"学生", "小贩", "摊贩", "市民", "孩子", "女孩", "老人", "男童", "女童", "百姓", "居民"}

# 读取 Excel 文件
df = pd.read_excel("300事件.xlsx")
event_list = df["事件"].dropna().astype(str).tolist()

# 结果列表
results = []

# 主函数：处理每个事件句子
def process_text(text):
    try:
        # 分句
        sentences = splitter.split(text)
        for sent in sentences:
            # pipeline 获取多任务结果
            result = ltp.pipeline([sent], tasks=["cws", "pos", "dep"], return_dict=True)

            words = result["cws"][0]
            pos = result["pos"][0]
            dep = result["dep"][0]  # 格式为 (head, relation)

            subj = ""
            verb = ""
            obj = ""

            for i, (head, rel) in enumerate(dep):
                if rel == "SBV":  # 主谓
                    subj = words[i]
                    if head > 0:
                        verb = words[head - 1]
                elif rel == "VOB":  # 动宾
                    obj = words[i]

            if subj in strong_groups and obj in weak_groups:
                conclusion = f"{subj}欺凌{obj}（{subj}为强势群体）"
            elif subj in weak_groups and obj in strong_groups:
                conclusion = f"{subj}反抗{obj}（{obj}为强势群体）"
            elif subj and obj:
                conclusion = f"{subj} vs {obj}（无明显强弱关系）"
            else:
                conclusion = "信息不足，无法判断"

            results.append({
                "原句": sent,
                "主语": subj,
                "谓语": verb,
                "宾语": obj,
                "分析结果": conclusion
            })
    except Exception as e:
        results.append({
            "原句": text,
            "主语": "",
            "谓语": "",
            "宾语": "",
            "分析结果": f"分析失败：{str(e)}"
        })

# 批量处理
for event in event_list:
    process_text(event)

# 保存结果
result_df = pd.DataFrame(results)
result_df.to_excel("事件群体分析结果.xlsx", index=False)

print("✅ 分析完成，结果已保存为：事件群体分析结果.xlsx")

import pandas as pd
from ltp import LTP

# 读取Excel文件
file_path = "300事件.xlsx"  # 确保此文件与.py或.ipynb在同一目录
df = pd.read_excel(file_path)
events = df["事件"].dropna().tolist()

# 初始化LTP
ltp = LTP()

# 主谓宾提取函数
def extract_svo(event):
    res = ltp.pipeline([event], tasks=["cws", "pos", "dep"], return_dict=True)
    words = res["cws"][0]
    deps = res["dep"][0]

    subject = predicate = obj = ""

    for i, (head, relation) in enumerate(deps):
        if relation == "SBV":
            subject = words[i]
            predicate = words[head - 1]
        elif relation == "VOB":
            predicate = words[head - 1]
            obj = words[i]
        elif relation == "POB" and words[head - 1] == "被":
            # 被动语态处理：例如“他被打了” -> “某人打他”
            predicate = words[i]
            obj = words[head]
            subject = "未知"

    return subject, predicate, obj

# 应用到所有事件
result = []
for evt in events:
    sub, pred, ob = extract_svo(evt)
    result.append([evt, sub, pred, ob])

# 写入Excel
df_result = pd.DataFrame(result, columns=["事件", "主语", "谓语", "宾语"])
df_result.to_excel("事件主谓宾分析结果.xlsx", index=False)
print("分析完成，文件已保存为：事件主谓宾分析结果.xlsx")