import pandas as pd
import os

def view_analysis_results():
    """
    查看分析结果
    """
    desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
    
    # 查找最新的分析结果文件
    files = [f for f in os.listdir(desktop_path) if f.startswith("事件_分析结果_") and f.endswith(".xlsx")]
    if not files:
        print("未找到分析结果文件")
        return
    
    latest_file = max(files)
    file_path = os.path.join(desktop_path, latest_file)
    
    print(f"读取文件: {latest_file}")
    
    try:
        df = pd.read_excel(file_path)
        
        print(f"\n总共分析了 {len(df)} 条新闻标题")
        print("\n=== 详细分析结果 ===")
        
        for idx, row in df.iterrows():
            print(f"\n{idx+1}. {row['原标题']}")
            print(f"   分词: {row['分词结果']}")
            print(f"   主语: {row['主语']} ({row['主语群体类型']})")
            print(f"   谓语: {row['谓语']}")
            print(f"   宾语: {row['宾语']} ({row['宾语群体类型']})")
            print(f"   群体关系: {row['群体关系']}")
            print(f"   涉及强势群体: {row['涉及强势群体']}")
            print(f"   涉及弱势群体: {row['涉及弱势群体']}")
        
        # 统计分析
        print("\n=== 统计分析 ===")
        
        # 群体关系统计
        relationship_counts = df['群体关系'].value_counts()
        print("\n群体关系分布:")
        for relation, count in relationship_counts.items():
            print(f"  {relation}: {count}条")
        
        # 强势群体统计
        strong_groups_mentioned = []
        for groups in df['涉及强势群体']:
            if groups != '无':
                strong_groups_mentioned.extend(groups.split(', '))
        
        if strong_groups_mentioned:
            from collections import Counter
            strong_counts = Counter(strong_groups_mentioned)
            print("\n涉及的强势群体:")
            for group, count in strong_counts.items():
                print(f"  {group}: {count}次")
        
        # 弱势群体统计
        weak_groups_mentioned = []
        for groups in df['涉及弱势群体']:
            if groups != '无':
                weak_groups_mentioned.extend(groups.split(', '))
        
        if weak_groups_mentioned:
            from collections import Counter
            weak_counts = Counter(weak_groups_mentioned)
            print("\n涉及的弱势群体:")
            for group, count in weak_counts.items():
                print(f"  {group}: {count}次")
        
    except Exception as e:
        print(f"读取文件时出错: {e}")

if __name__ == "__main__":
    view_analysis_results()
