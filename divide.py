import pandas as pd

# 设置显示选项，避免输出截断
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)

# 读取CSV文件（修复编码问题）
try:
    df = pd.read_csv('143广州一商贩持铁棍追打城管.csv', encoding='gb18030')
except UnicodeDecodeError:
    try:
        df = pd.read_csv('143广州一商贩持铁棍追打城管.csv', encoding='utf-8')
    except UnicodeDecodeError:
        df = pd.read_csv('143广州一商贩持铁棍追打城管.csv', encoding='gbk')

print("原始数据形状:", df.shape)
print("列名:", df.columns.tolist())

# 查看前几行数据，了解数据结构
print("\n前5行数据:")
print(df.head())

# 查看媒体类型的唯一值
if '媒体类型' in df.columns:
    print("\n媒体类型的唯一值:")
    print(df['媒体类型'].value_counts())

# 查看认证类型的唯一值
if '认证类型' in df.columns:
    print("\n认证类型的唯一值:")
    print(df['认证类型'].value_counts())

# 定义用户划分映射规则
def get_user_category(row):
    """
    根据媒体类型和认证类型确定用户划分
    """
    # 如果媒体类型不是"微博"，返回空白
    if row['媒体类型'] != '微博':
        return ''

    # 如果媒体类型是"微博"，根据认证类型进行划分
    auth_type = row['认证类型']

    # 用户划分映射
    user_mapping = {
        '普通用户': '普通公众',
        '个人认证-橙V': '活跃分子',
        '个人认证-金V': '意见领袖',
        '机构认证-媒体': '新闻媒体',
        '机构认证-团体': '商业/自媒体',
        '机构认证-政府': '政务机构',
        '个人认证-达人': ''
    }

    # 返回对应的用户划分，如果没有匹配则返回原认证类型
    return user_mapping.get(auth_type, auth_type)

# 创建新的"用户划分"列
df['用户划分'] = df.apply(get_user_category, axis=1)

print("\n添加用户划分列后的数据形状:", df.shape)

# 查看用户划分的分布情况
print("\n用户划分分布:")
print(df['用户划分'].value_counts())

# 查看微博数据的用户划分分布
weibo_data = df[df['媒体类型'] == '微博']
if not weibo_data.empty:
    print("\n微博数据的用户划分分布:")
    print(weibo_data['用户划分'].value_counts())

# 保存结果到新文件
output_filename = '143广州一商贩持铁棍追打城管.csv'
df.to_csv(output_filename, index=False, encoding='utf-8-sig')
print(f"\n结果已保存到: {output_filename}")

# 显示前几行结果，验证效果
print("\n处理后的前10行数据（只显示相关列）:")
relevant_columns = ['媒体类型', '认证类型', '用户划分']
if all(col in df.columns for col in relevant_columns):
    print(df[relevant_columns].head(10))
else:
    print("部分列不存在，显示所有列的前5行:")
    print(df.head())